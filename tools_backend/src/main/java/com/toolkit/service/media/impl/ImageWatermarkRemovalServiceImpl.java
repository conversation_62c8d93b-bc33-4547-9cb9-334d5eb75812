package com.toolkit.service.media.impl;

import com.toolkit.dto.media.ImageWatermarkRemovalDTO;
import com.toolkit.dto.media.ImageProcessResult;
import com.toolkit.service.media.ImageWatermarkRemovalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 图片去水印服务实现
 * 集成video-subtitle-remover工具的图片处理功能
 */
@Slf4j
@Service
public class ImageWatermarkRemovalServiceImpl implements ImageWatermarkRemovalService {

    @Value("${app.image-processor.work-dir:./temp/image-processor}")
    private String workDir;

    @Value("${app.image-processor.python-script:./scripts/image_watermark_processor.py}")
    private String pythonScriptPath;

    @Value("${app.image-processor.python-command:python}")
    private String pythonCommand;

    @Value("${app.image-processor.output-url-prefix:http://localhost:8080/file/processed/}")
    private String outputUrlPrefix;

    // 存储任务状态和进度
    private final ConcurrentHashMap<String, ImageProcessResult> taskResults = new ConcurrentHashMap<>();
    
    // 线程池用于异步处理
    private final ExecutorService executorService = Executors.newFixedThreadPool(4);

    @Override
    public Map<String, Object> processImages(ImageWatermarkRemovalDTO request) {
        // 生成任务ID
        String taskId = UUID.randomUUID().toString();
        
        log.info("开始处理图片去水印任务 - 任务ID: {}, 图片数量: {}, 算法: {}", 
                taskId, request.getImageFiles().size(), request.getAlgorithm());

        // 初始化任务结果
        ImageProcessResult result = ImageProcessResult.builder()
                .taskId(taskId)
                .status("PENDING")
                .progress(0)
                .totalCount(request.getImageFiles().size())
                .successCount(0)
                .failedCount(0)
                .startTime(LocalDateTime.now())
                .results(new ArrayList<>())
                .processConfig(createProcessConfig(request))
                .build();

        taskResults.put(taskId, result);

        // 异步处理图片
        CompletableFuture.runAsync(() -> processImagesAsync(taskId, request), executorService);

        // 返回任务信息
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "图片去水印任务已启动");
        response.put("taskId", taskId);
        response.put("totalCount", request.getImageFiles().size());
        response.put("status", "PENDING");
        response.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        return response;
    }

    @Override
    public Map<String, Object> processSingleImage(MultipartFile imageFile, String algorithm, 
                                                Boolean autoDetect, String outputFormat, String quality) {
        // 创建单图片处理请求
        ImageWatermarkRemovalDTO request = new ImageWatermarkRemovalDTO();
        request.setImageFiles(Arrays.asList(imageFile));
        request.setAlgorithm(algorithm);
        request.setAutoDetect(autoDetect);
        request.setOutputFormat(outputFormat);
        request.setQuality(quality);

        return processImages(request);
    }

    @Override
    public Integer getProcessProgress(String taskId) {
        ImageProcessResult result = taskResults.get(taskId);
        return result != null ? result.getProgress() : 0;
    }

    @Override
    public Map<String, Object> getProcessResult(String taskId) {
        ImageProcessResult result = taskResults.get(taskId);
        if (result == null) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "任务不存在");
            return response;
        }

        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("taskId", taskId);
        response.put("status", result.getStatus());
        response.put("progress", result.getProgress());
        response.put("totalCount", result.getTotalCount());
        response.put("successCount", result.getSuccessCount());
        response.put("failedCount", result.getFailedCount());
        response.put("results", result.getResults());
        response.put("startTime", result.getStartTime());
        response.put("endTime", result.getEndTime());
        response.put("processingTimeSeconds", result.getProcessingTimeSeconds());
        response.put("errorMessage", result.getErrorMessage());

        return response;
    }

    @Override
    public void cleanupTask(String taskId) {
        ImageProcessResult result = taskResults.remove(taskId);
        if (result != null) {
            // 清理临时文件
            try {
                Path taskDir = Paths.get(workDir, taskId);
                if (Files.exists(taskDir)) {
                    Files.walk(taskDir)
                            .sorted(Comparator.reverseOrder())
                            .map(Path::toFile)
                            .forEach(File::delete);
                }
                log.info("已清理任务临时文件 - 任务ID: {}", taskId);
            } catch (Exception e) {
                log.error("清理任务临时文件失败 - 任务ID: {}", taskId, e);
            }
        }
    }

    @Override
    public String getTaskStatus(String taskId) {
        ImageProcessResult result = taskResults.get(taskId);
        return result != null ? result.getStatus() : "NOT_FOUND";
    }

    @Override
    public Map<String, Object> getSupportedAlgorithms() {
        Map<String, Object> algorithms = new HashMap<>();
        
        Map<String, Object> lama = new HashMap<>();
        lama.put("name", "LAMA");
        lama.put("description", "效果好，适合各种类型的图片水印去除");
        lama.put("speed", "快");
        lama.put("quality", "高");
        lama.put("recommended", true);
        algorithms.put("lama", lama);
        
        Map<String, Object> sttn = new HashMap<>();
        sttn.put("name", "STTN");
        sttn.put("description", "适合复杂背景的水印去除");
        sttn.put("speed", "中等");
        sttn.put("quality", "高");
        sttn.put("recommended", false);
        algorithms.put("sttn", sttn);
        
        return algorithms;
    }

    /**
     * 异步处理图片
     */
    private void processImagesAsync(String taskId, ImageWatermarkRemovalDTO request) {
        ImageProcessResult result = taskResults.get(taskId);
        if (result == null) {
            return;
        }

        try {
            result.setStatus("PROCESSING");
            
            // 创建工作目录
            Path taskDir = Paths.get(workDir, taskId);
            Files.createDirectories(taskDir);
            Path inputDir = taskDir.resolve("input");
            Path outputDir = taskDir.resolve("output");
            Files.createDirectories(inputDir);
            Files.createDirectories(outputDir);

            List<ImageProcessResult.SingleImageResult> results = new ArrayList<>();
            int processedCount = 0;

            for (MultipartFile imageFile : request.getImageFiles()) {
                try {
                    // 处理单张图片
                    ImageProcessResult.SingleImageResult singleResult = processSingleImageFile(
                            imageFile, request, inputDir, outputDir, taskId);
                    results.add(singleResult);
                    
                    if ("SUCCESS".equals(singleResult.getStatus())) {
                        result.setSuccessCount(result.getSuccessCount() + 1);
                    } else {
                        result.setFailedCount(result.getFailedCount() + 1);
                    }
                    
                    processedCount++;
                    result.setProgress((processedCount * 100) / request.getImageFiles().size());
                    
                } catch (Exception e) {
                    log.error("处理图片失败 - 文件: {}", imageFile.getOriginalFilename(), e);
                    
                    ImageProcessResult.SingleImageResult errorResult = ImageProcessResult.SingleImageResult.builder()
                            .originalFileName(imageFile.getOriginalFilename())
                            .status("FAILED")
                            .errorMessage(e.getMessage())
                            .build();
                    results.add(errorResult);
                    result.setFailedCount(result.getFailedCount() + 1);
                    processedCount++;
                    result.setProgress((processedCount * 100) / request.getImageFiles().size());
                }
            }

            result.setResults(results);
            result.setStatus("COMPLETED");
            result.setProgress(100);
            result.setEndTime(LocalDateTime.now());
            
            // 计算处理时间
            if (result.getStartTime() != null && result.getEndTime() != null) {
                long seconds = java.time.Duration.between(result.getStartTime(), result.getEndTime()).getSeconds();
                result.setProcessingTimeSeconds(seconds);
            }

            log.info("图片去水印任务完成 - 任务ID: {}, 成功: {}, 失败: {}", 
                    taskId, result.getSuccessCount(), result.getFailedCount());

        } catch (Exception e) {
            log.error("图片去水印任务失败 - 任务ID: {}", taskId, e);
            result.setStatus("FAILED");
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now());
        }
    }

    /**
     * 处理单张图片文件
     */
    private ImageProcessResult.SingleImageResult processSingleImageFile(
            MultipartFile imageFile, ImageWatermarkRemovalDTO request,
            Path inputDir, Path outputDir, String taskId) throws Exception {

        long startTime = System.currentTimeMillis();
        String originalFileName = imageFile.getOriginalFilename();
        String fileExtension = getFileExtension(originalFileName);
        String baseFileName = getBaseFileName(originalFileName);

        // 保存输入文件
        Path inputFilePath = inputDir.resolve(originalFileName);
        imageFile.transferTo(inputFilePath.toFile());

        // 生成输出文件路径
        String outputFileName = baseFileName + "_processed." + request.getOutputFormat();
        Path outputFilePath = outputDir.resolve(outputFileName);

        try {
            // 调用Python脚本处理图片
            boolean success = callPythonProcessor(inputFilePath, outputFilePath, request);

            if (success && Files.exists(outputFilePath)) {
                // 生成URL
                String processedUrl = generateFileUrl(taskId, "output", outputFileName);
                String previewUrl = processedUrl; // 对于图片，预览URL和处理URL相同
                String downloadUrl = processedUrl;

                // 获取文件信息
                long fileSize = Files.size(outputFilePath);
                String dimensions = getImageDimensions(outputFilePath);

                long processingTime = System.currentTimeMillis() - startTime;

                return ImageProcessResult.SingleImageResult.builder()
                        .originalFileName(originalFileName)
                        .status("SUCCESS")
                        .processedUrl(processedUrl)
                        .previewUrl(previewUrl)
                        .downloadUrl(downloadUrl)
                        .fileSize(fileSize)
                        .dimensions(dimensions)
                        .processingTimeMs(processingTime)
                        .processingMethod(request.getAlgorithm().toUpperCase())
                        .qualityScore(85.0 + Math.random() * 10) // 模拟质量评分
                        .build();

            } else {
                throw new RuntimeException("图片处理失败，未生成输出文件");
            }

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            return ImageProcessResult.SingleImageResult.builder()
                    .originalFileName(originalFileName)
                    .status("FAILED")
                    .errorMessage(e.getMessage())
                    .processingTimeMs(processingTime)
                    .build();
        }
    }

    /**
     * 调用Python脚本处理图片
     */
    private boolean callPythonProcessor(Path inputPath, Path outputPath, ImageWatermarkRemovalDTO request) {
        try {
            List<String> command = new ArrayList<>();
            command.add(pythonCommand);
            command.add(pythonScriptPath);
            command.add("-i");
            command.add(inputPath.toString());
            command.add("-o");
            command.add(outputPath.getParent().toString());
            command.add("--algorithm");
            command.add(request.getAlgorithm());
            command.add("--quality");
            command.add(request.getQuality());

            if (request.isAutoDetect()) {
                command.add("--auto-detect");
            } else {
                command.add("--skip-detection");
            }

            // 如果有指定水印区域，添加相关参数
            if (request.getWatermarkAreas() != null && !request.getWatermarkAreas().trim().isEmpty()) {
                command.add("--watermark-areas");
                command.add(request.getWatermarkAreas());
            }

            log.info("执行Python命令: {}", String.join(" ", command));

            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.directory(new File(System.getProperty("user.dir")));
            processBuilder.redirectErrorStream(true);

            Process process = processBuilder.start();

            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    log.debug("Python输出: {}", line);
                }
            }

            int exitCode = process.waitFor();
            log.info("Python脚本执行完成，退出码: {}", exitCode);

            if (exitCode != 0) {
                log.error("Python脚本执行失败，输出: {}", output.toString());
            }

            return exitCode == 0;

        } catch (Exception e) {
            log.error("调用Python脚本失败", e);
            return false;
        }
    }

    /**
     * 生成文件URL
     */
    private String generateFileUrl(String taskId, String type, String fileName) {
        return outputUrlPrefix + "image/" + taskId + "/" + type + "/" + fileName;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }

    /**
     * 获取文件基础名称（不含扩展名）
     */
    private String getBaseFileName(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return fileName;
        }
        return fileName.substring(0, fileName.lastIndexOf("."));
    }

    /**
     * 获取图片尺寸信息
     */
    private String getImageDimensions(Path imagePath) {
        try {
            // 这里可以使用ImageIO或其他库获取图片尺寸
            // 暂时返回模拟数据
            return "1920x1080";
        } catch (Exception e) {
            log.warn("获取图片尺寸失败: {}", imagePath, e);
            return "未知";
        }
    }

    /**
     * 创建处理配置
     */
    private Map<String, Object> createProcessConfig(ImageWatermarkRemovalDTO request) {
        Map<String, Object> config = new HashMap<>();
        config.put("algorithm", request.getAlgorithm());
        config.put("strength", request.getStrength());
        config.put("outputFormat", request.getOutputFormat());
        config.put("quality", request.getQuality());
        config.put("autoDetect", request.isAutoDetect());
        config.put("preserveDetails", request.isPreserveDetails());
        config.put("watermarkAreas", request.getWatermarkAreas());
        return config;
    }
}
