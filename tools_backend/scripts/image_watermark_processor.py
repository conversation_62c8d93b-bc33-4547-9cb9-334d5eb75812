#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片去水印处理脚本
直接调用video-subtitle-remover工具的图片处理功能
"""

import os
import sys
import argparse
import subprocess
import json
import time
from pathlib import Path

# 添加video-subtitle-remover路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
video_remover_path = os.path.join(project_root, '..', 'video-subtitle-remover')
video_remover_backend = os.path.join(video_remover_path, 'backend')
video_remover_main = os.path.join(video_remover_backend, 'main.py')

# 检查video-subtitle-remover是否存在
VSR_AVAILABLE = os.path.exists(video_remover_main)

if not VSR_AVAILABLE:
    print(f"Warning: video-subtitle-remover not found at {video_remover_path}")
    print("Using fallback image processing...")

# 导入OpenCV用于fallback处理
try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    print("Warning: OpenCV not available")
    CV2_AVAILABLE = False


class ImageWatermarkProcessor:
    """图片去水印处理器"""

    def __init__(self, algorithm='lama'):
        self.algorithm = algorithm
        self.use_vsr = VSR_AVAILABLE
        self.video_remover_main = video_remover_main
        
    def process_image(self, input_path, output_path, auto_detect=True, watermark_areas=None):
        """
        处理单张图片去水印

        Args:
            input_path: 输入图片路径
            output_path: 输出图片路径
            auto_detect: 是否自动检测水印
            watermark_areas: 手动指定的水印区域列表

        Returns:
            dict: 处理结果
        """
        try:
            start_time = time.time()

            # 检查输入文件
            if not os.path.exists(input_path):
                raise FileNotFoundError(f"输入文件不存在: {input_path}")

            if not self._is_image_file(input_path):
                raise ValueError(f"不支持的图片格式: {input_path}")

            detected_watermarks = []

            if self.use_vsr:
                # 使用video-subtitle-remover工具处理
                success = self._process_with_vsr(input_path, output_path, auto_detect, watermark_areas)
                if success:
                    detected_watermarks = [{'type': 'text', 'confidence': 0.9}]  # 模拟检测结果
                else:
                    print("video-subtitle-remover处理失败，尝试fallback模式")
                    # 如果VSR失败，尝试fallback
                    if not CV2_AVAILABLE:
                        raise RuntimeError("video-subtitle-remover和OpenCV都不可用")
                    success = self._process_with_fallback(input_path, output_path, auto_detect, watermark_areas)
                    if success:
                        detected_watermarks = [{'type': 'unknown', 'confidence': 0.8}]
                    else:
                        raise RuntimeError("所有处理方法都失败了")
            else:
                # 使用fallback模式
                if not CV2_AVAILABLE:
                    raise RuntimeError("OpenCV不可用，无法进行fallback处理")

                success = self._process_with_fallback(input_path, output_path, auto_detect, watermark_areas)
                if success:
                    detected_watermarks = [{'type': 'unknown', 'confidence': 0.8}]
                else:
                    raise RuntimeError("Fallback处理失败")

            processing_time = time.time() - start_time

            # 获取图片信息
            image_info = self._get_image_info(input_path)

            # 返回处理结果
            result = {
                'success': True,
                'input_path': input_path,
                'output_path': output_path,
                'processing_time': processing_time,
                'algorithm': self.algorithm,
                'auto_detect': auto_detect,
                'detected_watermarks': detected_watermarks,
                'image_info': image_info
            }

            return result

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'input_path': input_path,
                'output_path': output_path,
                'algorithm': self.algorithm
            }
    
    def _process_with_vsr(self, input_path, output_path, auto_detect, watermark_areas=None):
        """
        使用video-subtitle-remover的LAMA算法处理图片
        """
        try:
            # 创建输出目录
            output_dir = os.path.dirname(output_path)
            os.makedirs(output_dir, exist_ok=True)

            print(f"使用video-subtitle-remover LAMA算法处理图片: {input_path}")

            # 使用vsr_wrapper.py来处理图片
            vsr_wrapper_path = os.path.join(os.path.dirname(__file__), 'vsr_wrapper.py')

            # 构建命令
            command = [
                sys.executable,
                vsr_wrapper_path,
                input_path,
                output_path
            ]

            # 如果有水印区域信息，添加到命令中
            if watermark_areas and not auto_detect:
                try:
                    # 解析水印区域信息
                    areas = json.loads(watermark_areas) if isinstance(watermark_areas, str) else watermark_areas
                    if areas and len(areas) > 0:
                        # 查找当前图片对应的水印区域
                        # 由于这是单张图片处理，我们使用第一个区域或者imageIndex=0的区域
                        target_area = None

                        # 优先查找imageIndex=0的区域
                        for area in areas:
                            if area.get("imageIndex", 0) == 0:
                                target_area = area
                                break

                        # 如果没找到，使用第一个区域
                        if not target_area and len(areas) > 0:
                            target_area = areas[0]

                        if target_area:
                            watermark_area_json = json.dumps({
                                "x": target_area.get("x", 0),
                                "y": target_area.get("y", 0),
                                "width": target_area.get("width", 280),
                                "height": target_area.get("height", 238)
                            })
                            command.append(watermark_area_json)
                            print(f"使用指定水印区域: {watermark_area_json}")
                        else:
                            print("未找到有效的水印区域信息")
                except Exception as e:
                    print(f"解析水印区域失败: {e}")
                    print(f"原始水印区域数据: {watermark_areas}")

            print(f"执行命令: {' '.join(command)}")

            # 切换到video-subtitle-remover目录并激活虚拟环境
            vsr_root = "/Users/<USER>/Desktop/Projects/AllTools/video-subtitle-remover"
            vsr_env = os.path.join(vsr_root, "videoEnv", "bin", "activate")

            # 构建完整的shell命令
            shell_command = f"cd {vsr_root} && source {vsr_env} && {' '.join(command)}"

            # 执行命令
            result = subprocess.run(
                shell_command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=180
            )

            print(f"LAMA处理退出码: {result.returncode}")
            if result.stdout:
                print(f"输出: {result.stdout}")
            if result.stderr:
                print(f"错误: {result.stderr}")

            if result.returncode == 0:
                # 检查输出文件是否存在
                if os.path.exists(output_path):
                    print(f"LAMA处理成功: {output_path}")
                    return True
                else:
                    print("LAMA处理完成但未生成输出文件")
                    return False
            else:
                print("LAMA处理失败")
                return False

        except subprocess.TimeoutExpired:
            print("LAMA处理超时")
            return False
        except Exception as e:
            print(f"LAMA处理异常: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _process_with_fallback(self, input_path, output_path, auto_detect, watermark_areas):
        """
        使用fallback模式处理图片
        """
        try:
            # 读取图片
            image = cv2.imread(input_path)
            if image is None:
                return False

            processed = image.copy()

            if auto_detect:
                # 简单的自动检测：处理图片边缘区域
                h, w = image.shape[:2]

                # 处理右下角区域（常见水印位置）
                corner_size = min(w//4, h//4, 200)
                processed = self._simple_inpaint(processed, w-corner_size, w, h-corner_size, h)

            elif watermark_areas:
                # 处理手动指定的区域
                coords_list = self._parse_watermark_areas(watermark_areas)
                for coords in coords_list:
                    xmin, xmax, ymin, ymax = coords
                    processed = self._simple_inpaint(processed, xmin, xmax, ymin, ymax)

            # 保存处理后的图片
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            success = cv2.imwrite(output_path, processed)

            return success

        except Exception as e:
            print(f"Fallback处理失败: {e}")
            return False

    def _simple_inpaint(self, image, xmin, xmax, ymin, ymax):
        """
        简单的图像修复
        """
        try:
            h, w = image.shape[:2]

            # 确保坐标在有效范围内
            xmin = max(0, min(xmin, w-1))
            xmax = max(xmin+1, min(xmax, w))
            ymin = max(0, min(ymin, h-1))
            ymax = max(ymin+1, min(ymax, h))

            # 创建mask
            mask = np.zeros((h, w), dtype=np.uint8)
            mask[ymin:ymax, xmin:xmax] = 255

            # 使用OpenCV的inpaint
            result = cv2.inpaint(image, mask, 3, cv2.INPAINT_TELEA)

            return result

        except Exception as e:
            print(f"简单修复失败: {e}")
            return image
    
    def _parse_watermark_areas(self, watermark_areas_str):
        """解析水印区域字符串"""
        try:
            if not watermark_areas_str:
                return []

            areas = json.loads(watermark_areas_str)
            mask_coords = []

            for area in areas:
                x = area.get('x', 0)
                y = area.get('y', 0)
                width = area.get('width', 0)
                height = area.get('height', 0)

                # 转换为(xmin, xmax, ymin, ymax)格式
                xmin = x
                xmax = x + width
                ymin = y
                ymax = y + height

                mask_coords.append((xmin, xmax, ymin, ymax))

            return mask_coords

        except Exception as e:
            print(f"解析水印区域失败: {e}")
            return []

    def _get_image_info(self, image_path):
        """获取图片信息"""
        try:
            if CV2_AVAILABLE:
                image = cv2.imread(image_path)
                if image is not None:
                    return {
                        'width': image.shape[1],
                        'height': image.shape[0],
                        'channels': image.shape[2] if len(image.shape) > 2 else 1
                    }

            # Fallback: 使用文件大小估算
            file_size = os.path.getsize(image_path)
            return {
                'width': 1000,  # 估算值
                'height': 800,  # 估算值
                'channels': 3,
                'file_size': file_size
            }

        except Exception as e:
            print(f"获取图片信息失败: {e}")
            return {'width': 0, 'height': 0, 'channels': 0}

    def _is_image_file(self, file_path):
        """检查是否为图片文件"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff'}
        return Path(file_path).suffix.lower() in image_extensions

    def _fallback_process(self, image, auto_detect, watermark_areas):
        """
        Fallback图像处理（当LAMA不可用时）
        使用改进的图像修复技术
        """
        try:
            processed = image.copy()

            if auto_detect:
                # 自动检测水印区域（简化版）
                watermark_regions = self._detect_watermark_regions(image)
                for region in watermark_regions:
                    processed = self._inpaint_region(processed, region)

            # 如果有指定区域，进行局部处理
            if watermark_areas:
                mask_coords = self._parse_watermark_areas(watermark_areas)
                for coords in mask_coords:
                    xmin, xmax, ymin, ymax = coords
                    # 确保坐标在图像范围内
                    h, w = image.shape[:2]
                    xmin = max(0, min(xmin, w-1))
                    xmax = max(xmin+1, min(xmax, w))
                    ymin = max(0, min(ymin, h-1))
                    ymax = max(ymin+1, min(ymax, h))

                    # 使用改进的修复算法
                    processed = self._inpaint_region(processed, (xmin, xmax, ymin, ymax))

            return processed

        except Exception as e:
            print(f"Fallback处理失败: {e}")
            return image

    def _detect_watermark_regions(self, image):
        """
        简化的水印检测（检测图像边缘区域的异常）
        """
        try:
            h, w = image.shape[:2]
            regions = []

            # 检查四个角落区域
            corner_size = min(w//4, h//4, 200)

            # 右下角（常见水印位置）
            regions.append((w-corner_size, w, h-corner_size, h))

            # 右上角
            regions.append((w-corner_size, w, 0, corner_size))

            # 左下角
            regions.append((0, corner_size, h-corner_size, h))

            # 左上角
            regions.append((0, corner_size, 0, corner_size))

            return regions

        except Exception as e:
            print(f"水印检测失败: {e}")
            return []

    def _inpaint_region(self, image, region):
        """
        对指定区域进行图像修复
        """
        try:
            xmin, xmax, ymin, ymax = region
            h, w = image.shape[:2]

            # 确保坐标有效
            if xmin >= xmax or ymin >= ymax or xmin < 0 or ymin < 0 or xmax > w or ymax > h:
                return image

            # 创建mask
            mask = np.zeros((h, w), dtype=np.uint8)
            mask[ymin:ymax, xmin:xmax] = 255

            # 使用OpenCV的inpaint函数
            result = cv2.inpaint(image, mask, 3, cv2.INPAINT_TELEA)

            # 如果inpaint效果不好，使用自定义的修复方法
            if np.array_equal(result, image):
                result = self._custom_inpaint(image, region)

            return result

        except Exception as e:
            print(f"区域修复失败: {e}")
            return image

    def _custom_inpaint(self, image, region):
        """
        自定义的图像修复算法
        """
        try:
            xmin, xmax, ymin, ymax = region
            processed = image.copy()

            # 获取周围区域的像素用于填充
            roi = processed[ymin:ymax, xmin:xmax]
            h_roi, w_roi = roi.shape[:2]

            # 方法1: 使用周围像素的平均值
            border_size = 10

            # 获取上边界
            if ymin >= border_size:
                top_border = processed[ymin-border_size:ymin, xmin:xmax]
                top_mean = np.mean(top_border, axis=0)
            else:
                top_mean = None

            # 获取下边界
            if ymax + border_size < image.shape[0]:
                bottom_border = processed[ymax:ymax+border_size, xmin:xmax]
                bottom_mean = np.mean(bottom_border, axis=0)
            else:
                bottom_mean = None

            # 获取左边界
            if xmin >= border_size:
                left_border = processed[ymin:ymax, xmin-border_size:xmin]
                left_mean = np.mean(left_border, axis=1)
            else:
                left_mean = None

            # 获取右边界
            if xmax + border_size < image.shape[1]:
                right_border = processed[ymin:ymax, xmax:xmax+border_size]
                right_mean = np.mean(right_border, axis=1)
            else:
                right_mean = None

            # 使用双线性插值填充区域
            for y in range(h_roi):
                for x in range(w_roi):
                    # 计算权重
                    weight_top = (h_roi - y) / h_roi if top_mean is not None else 0
                    weight_bottom = y / h_roi if bottom_mean is not None else 0
                    weight_left = (w_roi - x) / w_roi if left_mean is not None else 0
                    weight_right = x / w_roi if right_mean is not None else 0

                    # 归一化权重
                    total_weight = weight_top + weight_bottom + weight_left + weight_right
                    if total_weight > 0:
                        weight_top /= total_weight
                        weight_bottom /= total_weight
                        weight_left /= total_weight
                        weight_right /= total_weight

                        # 计算新像素值
                        new_pixel = np.zeros(3)
                        if top_mean is not None and x < len(top_mean):
                            new_pixel += weight_top * top_mean[x]
                        if bottom_mean is not None and x < len(bottom_mean):
                            new_pixel += weight_bottom * bottom_mean[x]
                        if left_mean is not None and y < len(left_mean):
                            new_pixel += weight_left * left_mean[y]
                        if right_mean is not None and y < len(right_mean):
                            new_pixel += weight_right * right_mean[y]

                        processed[ymin + y, xmin + x] = new_pixel.astype(np.uint8)

            # 应用轻微的高斯模糊来平滑边界
            roi_processed = processed[ymin:ymax, xmin:xmax]
            roi_blurred = cv2.GaussianBlur(roi_processed, (5, 5), 1)
            processed[ymin:ymax, xmin:xmax] = roi_blurred

            return processed

        except Exception as e:
            print(f"自定义修复失败: {e}")
            return image

    def _is_image_file(self, file_path):
        """检查是否为图片文件"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff'}
        return Path(file_path).suffix.lower() in image_extensions


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='图片去水印处理工具')
    parser.add_argument('-i', '--input', required=True, help='输入图片路径')
    parser.add_argument('-o', '--output', required=True, help='输出目录路径')
    parser.add_argument('--algorithm', choices=['lama', 'sttn'], default='lama', help='处理算法')
    parser.add_argument('--quality', choices=['low', 'medium', 'high', 'ultra'], default='high', help='输出质量')
    parser.add_argument('--output-format', choices=['png', 'jpg', 'jpeg', 'webp'], default='png', help='输出格式')
    parser.add_argument('--auto-detect', action='store_true', default=True, help='自动检测水印')
    parser.add_argument('--watermark-areas', help='手动指定水印区域（JSON格式）')
    parser.add_argument('--skip-detection', action='store_true', help='跳过水印检测')
    
    args = parser.parse_args()
    
    # 创建处理器
    processor = ImageWatermarkProcessor(algorithm=args.algorithm)
    
    # 构建输出路径
    input_path = args.input
    output_dir = args.output
    input_filename = os.path.basename(input_path)
    name, ext = os.path.splitext(input_filename)

    # 根据输出格式确定扩展名
    output_format = args.output_format if hasattr(args, 'output_format') else 'png'
    if output_format == 'jpg':
        output_ext = '.jpg'
    elif output_format == 'jpeg':
        output_ext = '.jpeg'
    elif output_format == 'png':
        output_ext = '.png'
    elif output_format == 'webp':
        output_ext = '.webp'
    else:
        output_ext = ext  # 保持原格式

    output_filename = f"{name}_processed{output_ext}"
    output_path = os.path.join(output_dir, output_filename)
    
    # 处理图片
    auto_detect = args.auto_detect and not args.skip_detection
    result = processor.process_image(
        input_path=input_path,
        output_path=output_path,
        auto_detect=auto_detect,
        watermark_areas=args.watermark_areas
    )
    
    # 输出结果
    print(json.dumps(result, ensure_ascii=False, indent=2))
    
    # 返回退出码
    sys.exit(0 if result['success'] else 1)


if __name__ == '__main__':
    main()
