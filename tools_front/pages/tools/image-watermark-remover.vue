<template>
  <div class="min-h-screen bg-gray-50">
    <div class="p-4 space-y-4">
      <!-- 上传区域 -->
      <div class="bg-white rounded-lg shadow-sm p-4">
        <h3 class="font-semibold text-gray-900 mb-4 flex items-center">
          <Upload :size="18" class="mr-2 text-blue-500" />
          上传图片文件
        </h3>
        
        <div v-if="images.length === 0" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <ImageIcon :size="48" class="mx-auto text-gray-400 mb-4" />
          <p class="text-gray-600 mb-4">点击选择图片文件</p>
          <button
            @tap="handleChooseImage"
            class="bg-blue-500 text-white px-6 py-2 rounded-lg cursor-pointer hover-bg-blue-600 transition-colors"
          >
            选择图片文件
          </button>
          <p class="text-sm text-gray-500 mt-2">
            支持 JPG, PNG, GIF, WEBP 等格式，可批量选择
          </p>
        </div>
        
        <div v-else class="space-y-4">
          <div class="flex items-center justify-between">
            <p class="text-sm text-gray-600">已选择 {{ images.length }} 张图片</p>
            <div class="flex items-center gap-2">
              <button
                @tap="toggleWatermarkSelection"
                :class="[
                  'px-3 py-1 rounded-md text-sm transition-colors',
                  isWatermarkSelectionMode
                    ? 'bg-blue-500 text-white'
                    : 'border border-gray-300 hover-bg-gray-50'
                ]"
              >
                {{ isWatermarkSelectionMode ? '完成选择' : '选择水印区域' }}
              </button>
              <button
                @tap="handleReset"
                class="px-3 py-1 border border-gray-300 rounded-md hover-bg-gray-50 flex items-center"
              >
                <RotateCcw :size="16" />
              </button>
            </div>
          </div>

          <!-- 水印选择提示 -->
          <div v-if="isWatermarkSelectionMode" class="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p class="text-sm text-blue-800">
              <strong>水印区域选择模式：</strong>点击图片可以拖拽选择要去除的水印区域
            </p>
          </div>

          <div class="grid grid-cols-2 gap-3 max-h-80 overflow-y-auto">
            <!-- 已选择的图片 -->
            <div
              v-for="(url, index) in imageUrls"
              :key="index"
              class="relative"
            >
              <div
                class="relative w-full h-32 rounded-lg overflow-hidden"
                @tap="isWatermarkSelectionMode ? openWatermarkSelector(index) : null"
              >
                <img
                  :src="url"
                  :alt="`Preview ${index + 1}`"
                  class="w-full h-full object-cover"
                  :class="{ 'cursor-pointer': isWatermarkSelectionMode }"
                />

                <!-- 水印区域覆盖层 -->
                <div
                  v-if="watermarkAreas[index]"
                  class="absolute border-2 border-red-500 bg-red-500 bg-opacity-20"
                  :style="getWatermarkAreaStyle(watermarkAreas[index], index)"
                >
                  <div class="absolute -top-6 left-0 bg-red-500 text-white text-xs px-2 py-1 rounded">
                    水印区域
                  </div>
                </div>
              </div>

              <button
                v-if="!isWatermarkSelectionMode"
                @tap="removeImage(index)"
                class="absolute top-1 right-1 bg-red-500 text-white p-1 rounded-full hover-bg-red-600"
              >
                <Trash2 :size="12" />
              </button>
              <div class="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                {{ getFileName(images[index]) }}
              </div>
            </div>
            
            <!-- 添加更多图片的按钮 -->
            <view
              @tap="handleChooseImage"
              class="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center hover:bg-gray-50 transition-colors cursor-pointer"
            >
              <Plus :size="24" class="text-gray-400 mb-2" />
              <text class="text-sm text-gray-500">添加</text>
            </view>
          </div>
        </div>
      </div>

      <!-- 处理按钮 -->
      <button
        v-if="images.length > 0 && processedImages.length === 0"
        @tap="handleProcess"
        :disabled="isProcessing"
        class="w-full py-6 text-lg font-semibold bg-blue-600 text-white rounded-md hover-bg-blue-700 disabled-bg-gray-400 disabled-cursor-not-allowed"
      >
        {{ isProcessing ? `批量处理中... ${Math.round(progress)}%` : '开始批量去水印' }}
      </button>

      <!-- 进度条 -->
      <div v-if="isProcessing" class="bg-white rounded-2xl p-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium">处理进度</span>
          <span class="text-sm text-gray-600">{{ Math.round(progress) }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2">
          <div
            class="bg-blue-500 h-2 rounded-full transition-all duration-300"
            :style="{ width: `${progress}%` }"
          />
        </div>
      </div>

      <!-- 处理结果 -->
      <div v-if="processedImages.length > 0" class="bg-white rounded-2xl p-4">
        <h3 class="font-semibold text-gray-900 mb-3">处理完成</h3>
        <div class="grid grid-cols-2 gap-3 max-h-80 overflow-y-auto mb-4">
          <div
            v-for="(url, index) in processedImages"
            :key="index"
            class="relative group cursor-pointer"
            @tap="handlePreview(url)"
          >
            <img
              :src="url"
              :alt="`Processed ${index + 1}`"
              class="w-full h-32 object-cover rounded-lg"
            />
            <div class="absolute top-1 left-1 bg-green-500 text-white text-xs px-2 py-1 rounded">
              已处理
            </div>
            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover-bg-opacity-40 transition-all duration-300 rounded-lg flex items-center justify-center">
              <div class="opacity-0 group-hover-opacity-100 transform translate-y-2 group-hover-translate-y-0 transition-all duration-300 flex flex-col items-center gap-2">
                <button
                  @tap.stop="handleDownloadSingle(url, index)"
                  class="bg-blue-500 text-white px-4 py-2 rounded-lg flex items-center shadow-lg hover:bg-blue-600 active:scale-95"
                >
                  <Download :size="16" class="mr-2" />
                  <text>下载</text>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="flex gap-3">
          <button
            @tap="handleDownloadAll"
            class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover-bg-blue-700 flex items-center justify-center"
          >
            <Download class="mr-2" :size="16" />
            打包下载全部
          </button>
          <button
            @tap="handleReset"
            class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover-bg-gray-50 flex items-center justify-center"
          >
            <RotateCcw class="mr-2" :size="16" />
            重新处理
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 预览弹窗 -->
  <view v-if="showPreview" class="preview-modal">
    <view class="preview-content" @tap.stop>
      <!-- 图片预览 -->
      <image :src="currentPreviewImage" class="preview-image" mode="aspectFit" />

      <!-- 关闭按钮 -->
      <view class="close-btn" @tap="closePreview">
        <text class="close-icon">×</text>
      </view>

      <!-- 底部下载按钮 -->
      <view class="download-btn" @tap="handleDownloadSingle(currentPreviewImage, processedImages.indexOf(currentPreviewImage))">
        <text class="download-text">下载此图片</text>
      </view>
    </view>
    <view class="preview-mask" @tap="closePreview"></view>
  </view>

  <!-- 水印区域选择弹窗 -->
  <view v-if="showWatermarkSelector" class="watermark-selector-modal">
    <view class="watermark-selector-content" @tap.stop>
      <!-- 标题栏 -->
      <view class="selector-header">
        <text class="selector-title">选择水印区域</text>
        <view class="selector-actions">
          <button @tap="clearWatermarkArea" class="action-btn clear-btn">清除</button>
          <button @tap="confirmWatermarkArea" class="action-btn confirm-btn">确认</button>
          <button @tap="closeWatermarkSelector" class="action-btn close-btn">×</button>
        </view>
      </view>

      <!-- 图片容器 -->
      <view class="image-container" ref="imageContainer">
        <image
          :src="currentSelectorImage"
          class="selector-image"
          mode="aspectFit"
          @load="onImageLoad"
          @touchstart="onTouchStart"
          @touchmove="onTouchMove"
          @touchend="onTouchEnd"
        />

        <!-- 选择框 -->
        <view
          v-if="selectionBox.visible"
          class="selection-box"
          :style="selectionBoxStyle"
        >
          <view class="selection-border"></view>
          <text class="selection-label">水印区域</text>
        </view>
      </view>

      <!-- 提示信息 -->
      <view class="selector-tips">
        <text class="tip-text">拖拽选择要去除的水印区域，松开手指完成选择</text>
      </view>
    </view>
    <view class="watermark-selector-mask" @tap="closeWatermarkSelector"></view>
  </view>
</template>

<script>
import { Upload, Download, ImageIcon, RotateCcw, Trash2, Plus } from 'lucide-vue-next'
import { API_ENDPOINTS, httpRequest } from '@/config/api.js'

export default {
  name: 'ImageWatermarkRemover',
  components: {
    Upload,
    Download,
    ImageIcon,
    RotateCcw,
    Trash2,
    Plus
  },
  data() {
    return {
      images: [],
      imageUrls: [],
      isProcessing: false,
      processedImages: [],
      progress: 0,
      showPreview: false,
      currentPreviewImage: '',

      // 水印区域选择相关
      isWatermarkSelectionMode: false,
      showWatermarkSelector: false,
      currentSelectorImage: '',
      currentSelectorIndex: -1,
      watermarkAreas: {}, // 存储每张图片的水印区域 {imageIndex: {x, y, width, height}}

      // 选择框相关
      selectionBox: {
        visible: false,
        startX: 0,
        startY: 0,
        endX: 0,
        endY: 0
      },
      imageInfo: {
        width: 0,
        height: 0,
        displayWidth: 0,
        displayHeight: 0
      },
      isDragging: false
    }
  },
  computed: {
    selectionBoxStyle() {
      if (!this.selectionBox.visible) return {}

      const left = Math.min(this.selectionBox.startX, this.selectionBox.endX)
      const top = Math.min(this.selectionBox.startY, this.selectionBox.endY)
      const width = Math.abs(this.selectionBox.endX - this.selectionBox.startX)
      const height = Math.abs(this.selectionBox.endY - this.selectionBox.startY)

      return {
        left: `${left}px`,
        top: `${top}px`,
        width: `${width}px`,
        height: `${height}px`
      }
    }
  },
  methods: {
    handleChooseImage() {
      // #ifdef MP-WEIXIN
      uni.chooseImage({
        count: 9 - this.images.length, // 最多可选择的数量为9减去已有图片数
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          const tempFiles = res.tempFiles.map(file => ({
            ...file,
            path: file.path || file.tempFilePath
          }));
          
          // 追加新选择的图片
          this.images = [...this.images, ...tempFiles];
          this.imageUrls = [...this.imageUrls, ...tempFilePaths];
          this.processedImages = [];
          this.progress = 0;
        },
        fail: (err) => {
          console.error('选择图片失败:', err);
          uni.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      });
      // #endif
      
      // #ifdef H5
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.multiple = true;

      input.onchange = (event) => {
        const files = Array.from(event.target.files || []);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));

        if (imageFiles.length > 0) {
          // 追加新选择的图片
          this.images = [...this.images, ...imageFiles];
          const newUrls = imageFiles.map(file => URL.createObjectURL(file));
          this.imageUrls = [...this.imageUrls, ...newUrls];
          this.processedImages = [];
          this.progress = 0;
        }
      };

      input.click();
      // #endif
    },
    async handleProcess() {
      if (this.images.length === 0) return

      this.isProcessing = true
      this.progress = 0

      try {
        // 创建FormData
        const formData = new FormData()

        // 添加图片文件
        this.images.forEach((image, index) => {
          formData.append('files', image)
        })

        // 添加处理参数
        formData.append('algorithm', 'lama')
        formData.append('outputFormat', 'png')
        formData.append('quality', 'high')

        // 检查是否有手动选择的水印区域
        const hasWatermarkAreas = Object.keys(this.watermarkAreas).length > 0
        formData.append('autoDetect', hasWatermarkAreas ? 'false' : 'true')
        formData.append('preserveDetails', 'true')

        // 如果有手动选择的水印区域，添加区域信息
        if (hasWatermarkAreas) {
          const watermarkAreasArray = []
          Object.keys(this.watermarkAreas).forEach(imageIndex => {
            const area = this.watermarkAreas[imageIndex]
            watermarkAreasArray.push({
              imageIndex: parseInt(imageIndex),
              x: area.x,
              y: area.y,
              width: area.width,
              height: area.height
            })
          })
          formData.append('watermarkAreas', JSON.stringify(watermarkAreasArray))
        }

        // 发送请求到后端API
        const responseData = await httpRequest.postFormData(
          API_ENDPOINTS.IMAGE_WATERMARK_REMOVAL.BATCH,
          formData
        )

        if (responseData && responseData.success) {
          const taskId = responseData.data.taskId

          // 轮询获取处理进度
          await this.pollProgress(taskId)
        } else {
          throw new Error(responseData?.message || '处理失败')
        }

      } catch (error) {
        console.error('图片处理失败:', error)
        this.isProcessing = false
        uni.showToast({
          title: '处理失败: ' + (error.message || '未知错误'),
          icon: 'none'
        })
      }
    },

    async pollProgress(taskId) {
      const pollInterval = setInterval(async () => {
        try {
          const progressResult = await httpRequest.get(
            `${API_ENDPOINTS.IMAGE_WATERMARK_REMOVAL.PROGRESS}/${taskId}`
          )

          if (progressResult && progressResult.success) {
            const progressData = progressResult.data
            this.progress = progressData.progress || 0

            if (progressData.status === 'COMPLETED') {
              clearInterval(pollInterval)
              await this.getProcessResult(taskId)
            } else if (progressData.status === 'FAILED') {
              clearInterval(pollInterval)
              throw new Error('处理失败')
            }
          }
        } catch (error) {
          clearInterval(pollInterval)
          console.error('获取进度失败:', error)
          this.isProcessing = false
          uni.showToast({
            title: '获取进度失败',
            icon: 'none'
          })
        }
      }, 1000) // 每秒轮询一次
    },

    async getProcessResult(taskId) {
      try {
        const resultResult = await httpRequest.get(
          `${API_ENDPOINTS.IMAGE_WATERMARK_REMOVAL.RESULT}/${taskId}`
        )

        if (resultResult && resultResult.success) {
          const resultData = resultResult.data

          if (resultData.results && resultData.results.length > 0) {
            this.processedImages = resultData.results
              .filter(result => result.status === 'SUCCESS')
              .map(result => result.processedUrl)
          }

          this.isProcessing = false
          this.progress = 100

          uni.showToast({
            title: `处理完成！成功: ${resultData.successCount}, 失败: ${resultData.failedCount}`,
            icon: 'success'
          })
        } else {
          throw new Error('获取结果失败')
        }
      } catch (error) {
        console.error('获取结果失败:', error)
        this.isProcessing = false
        uni.showToast({
          title: '获取结果失败',
          icon: 'none'
        })
      }
    },
    async handleDownloadAll() {
      if (this.processedImages.length === 0) {
        uni.showToast({
          title: '没有可下载的图片',
          icon: 'none'
        });
        return;
      }

      try {
        // 显示下载中的提示
        uni.showLoading({
          title: '正在保存...',
          mask: true
        });

        // #ifdef MP-WEIXIN
        // 获取用户授权
        const auth = await uni.authorize({
          scope: 'scope.writePhotosAlbum'
        }).catch(() => null);

        if (!auth) {
          uni.hideLoading();
          // 如果用户拒绝授权，引导用户开启权限
          const res = await uni.showModal({
            title: '提示',
            content: '需要保存到相册的权限，是否前往设置？',
            confirmText: '前往设置'
          });
          
          if (res.confirm) {
            await uni.openSetting();
          }
          return;
        }

        // 批量保存图片到相册
        let savedCount = 0;
        for (const imageUrl of this.processedImages) {
          try {
            await uni.saveImageToPhotosAlbum({
              filePath: imageUrl
            });
            savedCount++;
          } catch (err) {
            console.error('保存图片失败:', err);
          }
        }

        uni.hideLoading();
        if (savedCount > 0) {
          uni.showToast({
            title: `成功保存${savedCount}张图片`,
            icon: 'success'
          });
        } else {
          uni.showToast({
            title: '保存失败',
            icon: 'error'
          });
        }
        // #endif

        // #ifdef H5
        for (let i = 0; i < this.processedImages.length; i++) {
          const url = this.processedImages[i];
          const fileName = `processed-image-${i + 1}.jpg`;
          
          const response = await fetch(url);
          const blob = await response.blob();
          const blobUrl = window.URL.createObjectURL(blob);
          
          const link = document.createElement('a');
          link.href = blobUrl;
          link.download = fileName;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          window.URL.revokeObjectURL(blobUrl);
        }

        uni.showToast({
          title: '开始下载',
          icon: 'success'
        });
        // #endif

      } catch (error) {
        console.error('下载失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        });
      }
    },
    handleReset() {
      this.images = []
      this.imageUrls = []
      this.processedImages = []
      this.progress = 0
      this.isProcessing = false
    },
    removeImage(index) {
      this.images = this.images.filter((_, i) => i !== index)
      this.imageUrls = this.imageUrls.filter((_, i) => i !== index)
    },
    getFileName(file) {
      // #ifdef MP-WEIXIN
      // 微信小程序环境
      if (!file) return '未知文件';
      const path = file.path || '';
      const name = path.split('/').pop() || '未知文件';
      return name.length > 15 ? name.substring(0, 15) + '...' : name;
      // #endif
      
      // #ifdef H5
      // H5环境
      if (!file) return '未知文件';
      const name = file.name || '未知文件';
      return name.length > 15 ? name.substring(0, 15) + '...' : name;
      // #endif
      
      return '未知文件';
    },
    async handleDownloadSingle(imageUrl, index) {
      try {
        // #ifdef MP-WEIXIN
        // 显示下载中的提示
        uni.showLoading({
          title: '正在保存...',
          mask: true
        });
        
        // 获取用户授权
        const auth = await uni.authorize({
          scope: 'scope.writePhotosAlbum'
        }).catch(() => null);
        
        if (!auth) {
          uni.hideLoading();
          const res = await uni.showModal({
            title: '提示',
            content: '需要保存到相册的权限，是否前往设置？',
            confirmText: '前往设置'
          });
          
          if (res.confirm) {
            await uni.openSetting();
          }
          return;
        }
        
        // 保存图片到相册
        await uni.saveImageToPhotosAlbum({
          filePath: imageUrl
        });
        
        uni.hideLoading();
        uni.showToast({
          title: '已保存到相册',
          icon: 'success'
        });
        // #endif
        
        // #ifdef H5
        const fileName = `processed-image-${index + 1}.jpg`;
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        const blobUrl = window.URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(blobUrl);
        
        uni.showToast({
          title: '开始下载',
          icon: 'success'
        });
        // #endif
        
      } catch (error) {
        console.error('下载失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: '保存失败',
          icon: 'error'
        });
      }
    },
    // 添加预览方法
    handlePreview(url) {
      console.log('预览图片:', url);
      console.log('当前showPreview状态:', this.showPreview);
      this.showPreview = true;
      this.currentPreviewImage = url;
      console.log('设置后showPreview状态:', this.showPreview);
      console.log('设置后currentPreviewImage:', this.currentPreviewImage);
    },
    closePreview() {
      this.showPreview = false;
      this.currentPreviewImage = '';
    },

    // 水印区域选择相关方法
    toggleWatermarkSelection() {
      this.isWatermarkSelectionMode = !this.isWatermarkSelectionMode
      if (!this.isWatermarkSelectionMode) {
        // 退出选择模式时，清除所有选择
        this.watermarkAreas = {}
      }
    },

    openWatermarkSelector(imageIndex) {
      this.currentSelectorIndex = imageIndex
      this.currentSelectorImage = this.imageUrls[imageIndex]
      this.showWatermarkSelector = true
      this.selectionBox.visible = false
      this.isDragging = false
    },

    closeWatermarkSelector() {
      this.showWatermarkSelector = false
      this.currentSelectorIndex = -1
      this.currentSelectorImage = ''
      this.selectionBox.visible = false
      this.isDragging = false
    },

    confirmWatermarkArea() {
      if (this.selectionBox.visible && this.currentSelectorIndex >= 0) {
        const left = Math.min(this.selectionBox.startX, this.selectionBox.endX)
        const top = Math.min(this.selectionBox.startY, this.selectionBox.endY)
        const width = Math.abs(this.selectionBox.endX - this.selectionBox.startX)
        const height = Math.abs(this.selectionBox.endY - this.selectionBox.startY)

        // 保存水印区域信息
        this.$set(this.watermarkAreas, this.currentSelectorIndex, {
          x: left,
          y: top,
          width: width,
          height: height
        })

        uni.showToast({
          title: '水印区域已选择',
          icon: 'success'
        })
      }

      this.closeWatermarkSelector()
    },

    clearWatermarkArea() {
      this.selectionBox.visible = false
      if (this.currentSelectorIndex >= 0) {
        this.$delete(this.watermarkAreas, this.currentSelectorIndex)
      }
    },

    getWatermarkAreaStyle(area, imageIndex) {
      if (!area) return {}

      return {
        left: `${area.x}px`,
        top: `${area.y}px`,
        width: `${area.width}px`,
        height: `${area.height}px`
      }
    },

    onTouchStart(e) {
      if (!this.showWatermarkSelector) return

      const touch = e.touches[0]
      this.isDragging = true
      this.selectionBox.startX = touch.clientX
      this.selectionBox.startY = touch.clientY
      this.selectionBox.endX = touch.clientX
      this.selectionBox.endY = touch.clientY
      this.selectionBox.visible = true
    },

    onTouchMove(e) {
      if (!this.isDragging || !this.showWatermarkSelector) return

      const touch = e.touches[0]
      this.selectionBox.endX = touch.clientX
      this.selectionBox.endY = touch.clientY
    },

    onTouchEnd(e) {
      if (!this.isDragging || !this.showWatermarkSelector) return
      this.isDragging = false
    },

    onImageLoad(e) {
      const { width, height } = e.detail
      this.imageInfo.width = width
      this.imageInfo.height = height
      this.imageInfo.displayWidth = width
      this.imageInfo.displayHeight = height
    }
  }
}
</script>

<style scoped lang="scss">
.min-h-screen {
  min-height: 100vh;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-white {
  background-color: #ffffff;
}

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded {
  border-radius: 0.25rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-8 {
  padding: 2rem;
}

.p-1 {
  padding: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.text-gray-900 {
  color: #111827;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-600 {
  color: #4b5563;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-white {
  color: #ffffff;
}

.text-blue-500 {
  color: #3b82f6;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.bg-blue-600 {
  background-color: #2563eb;
}

.bg-red-500 {
  background-color: #ef4444;
}

.bg-green-500 {
  background-color: #10b981;
}

.bg-gray-200 {
  background-color: #e5e7eb;
}

.bg-gray-400 {
  background-color: #9ca3af;
}

.bg-black {
  background-color: #000000;
}

.bg-opacity-50 {
  background-color: rgba(0, 0, 0, 0.5);
}

.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-gray-300 {
  border-color: #d1d5db;
}

.border-dashed {
  border-style: dashed;
}

.hover-bg-blue-600:hover {
  background-color: #2563eb;
}

.hover-bg-blue-700:hover {
  background-color: #1d4ed8;
}

.hover-bg-red-600:hover {
  background-color: #dc2626;
}

.hover-bg-gray-50:hover {
  background-color: #f9fafb;
}

.disabled-bg-gray-400:disabled {
  background-color: #9ca3af;
}

.disabled-cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.w-full {
  width: 100%;
}

.h-32 {
  height: 8rem;
}

.h-2 {
  height: 0.5rem;
}

.max-h-80 {
  max-height: 20rem;
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-3 {
  gap: 0.75rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.object-cover {
  object-fit: cover;
}

.overflow-y-auto {
  overflow-y: auto;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.top-1 {
  top: 0.25rem;
}

.right-1 {
  right: 0.25rem;
}

.bottom-1 {
  bottom: 0.25rem;
}

.left-1 {
  left: 0.25rem;
}

.space-y-4 {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-300 {
  transition-duration: 300ms;
}

button {
  cursor: pointer;
}

input[type="file"] {
  &::-webkit-file-upload-button {
    cursor: pointer;
  }
}

.group {
  position: relative;
}

.group:hover .group-hover-bg-opacity-40 {
  background-color: rgba(0, 0, 0, 0.4);
}

.group:hover .group-hover-opacity-100 {
  opacity: 1;
}

.group:hover .group-hover-translate-y-0 {
  transform: translateY(0);
}

.bg-opacity-0 {
  background-color: rgba(0, 0, 0, 0);
}

.bg-opacity-40 {
  background-color: rgba(0, 0, 0, 0.4);
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.translate-y-2 {
  transform: translateY(0.5rem);
}

.translate-y-0 {
  transform: translateY(0);
}

.transform {
  transform: translateY(0.5rem);
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.fixed {
  position: fixed;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.z-50 {
  z-index: 50;
}

.max-w-sm {
  max-width: 24rem;
}

.max-h-96 {
  max-height: 24rem;
}

.object-contain {
  object-fit: contain;
}

.bg-opacity-90 {
  background-color: rgba(0, 0, 0, 0.9);
}

.bg-opacity-70 {
  background-color: rgba(0, 0, 0, 0.7);
}

.top-4 {
  top: 1rem;
}

.right-4 {
  right: 1rem;
}

.bottom-4 {
  bottom: 1rem;
}

.left-4 {
  left: 1rem;
}

.flex-col {
  flex-direction: column;
}

.gap-2 {
  gap: 0.5rem;
}

.cursor-pointer {
  cursor: pointer;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.active-scale-95:active {
  transform: scale(0.95);
}

.hover-bg-blue-600:hover {
  background-color: #2563eb;
}

.preview-modal {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
}

.preview-mask {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
}

.preview-content {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.close-btn {
  position: fixed;
  right: 30rpx;
  top: 30rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon {
  color: #fff;
  font-size: 48rpx;
}

.download-btn {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  transform: translateX(-50%);
  background: #3b82f6;
  color: #fff;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80%;
  max-width: 600rpx;
}

.download-text {
  color: #fff;
}

/* 水印选择器样式 */
.watermark-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
}

.watermark-selector-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
}

.watermark-selector-content {
  position: relative;
  width: 95%;
  height: 90%;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e5e5;
  background-color: #f8f9fa;
}

.selector-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.selector-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  border: none;
}

.clear-btn {
  background-color: #f56565;
  color: white;
}

.confirm-btn {
  background-color: #48bb78;
  color: white;
}

.close-btn {
  background-color: #a0aec0;
  color: white;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
}

.image-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
}

.selector-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.selection-box {
  position: absolute;
  border: 2px solid #ff4757;
  background-color: rgba(255, 71, 87, 0.2);
  pointer-events: none;
}

.selection-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px dashed #ff4757;
}

.selection-label {
  position: absolute;
  top: -24px;
  left: 0;
  background-color: #ff4757;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.selector-tips {
  padding: 12px 16px;
  background-color: #e3f2fd;
  border-top: 1px solid #e5e5e5;
}

.tip-text {
  font-size: 14px;
  color: #1976d2;
  text-align: center;
}
</style>