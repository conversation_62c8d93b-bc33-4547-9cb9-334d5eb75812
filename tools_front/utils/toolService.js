// 工具服务类
import { showLoading, hideLoading, showSuccess, showError } from './index.js'
import { authManager } from './authManager.js'

// API基础URL配置
const BASE_URL = 'http://localhost:8080'

/**
 * 工具服务类
 */
export class ToolService {
  constructor() {
    this.baseUrl = BASE_URL
  }

  /**
   * 执行工具
   * @param {string} toolIdentifier 工具标识符
   * @param {object} params 工具参数
   * @returns {Promise} 执行结果
   */
  async executeTool(toolIdentifier, params = {}) {
    try {
      showLoading('处理中...')
      
      // 尝试无感登录
      await this.ensureAuthenticated()
      
      const result = await this.callToolAPI(`/api/tools/${toolIdentifier}`, 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 确保用户已认证
   */
  async ensureAuthenticated() {
    try {
      // 如果已经有token，直接返回
      if (authManager.isLoggedIn()) {
        return true
      }

      // 尝试无感登录
      console.log('尝试无感登录...')
      const loginSuccess = await authManager.silentLogin()
      
      if (!loginSuccess) {
        console.warn('无感登录失败，但继续执行（工具接口已开放）')
      }
      
      return true
    } catch (error) {
      console.error('认证失败:', error)
      // 即使认证失败，也继续执行，因为工具接口已经开放
      return true
    }
  }

  // ============ 好玩推荐工具 ============

  /**
   * 趣味图片生成器
   */
  async generateFunImage(params) {
    try {
      showLoading('生成中...')
      
      // 尝试无感登录
      await this.ensureAuthenticated()
      
      const result = await this.callToolAPI('/api/tools/fun/fun-image-generator', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 获取永久会员
   */
  async getVipMembership(params) {
    try {
      showLoading('处理中...')
      const result = await this.callToolAPI('/api/tools/fun/get-vip-membership', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  // ============ 媒体工具 ============

  /**
   * 视频解析去水印
   */
  async removeVideoWatermark(videoUrl) {
    try {
      showLoading('解析中...')
      const result = await this.callToolAPI('/api/tools/media/video-watermark-remover', 'POST', { videoUrl })
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 图集解析去水印
   */
  async removeImageWatermark(imageUrls) {
    try {
      showLoading('处理中...')
      const result = await this.callToolAPI('/api/tools/media/image-watermark-remover', 'POST', { imageUrls })
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 视频下载
   */
  async downloadVideo(params) {
    try {
      showLoading('解析中...')
      // 确保参数格式正确
      const requestParams = {
        videoUrl: params.videoUrl || params.url, // 优先使用videoUrl参数
        quality: params.quality || 'high',
        format: params.format || 'mp4'
      }
      
      console.log('发送视频解析请求:', requestParams)
      const result = await this.callToolAPI('/api/tools/media/video-downloader', 'POST', requestParams)
      console.log('视频解析返回结果:', result)
      
      hideLoading()
      return result
    } catch (error) {
      console.error('视频解析失败:', error)
      hideLoading()
      throw error
    }
  }

  /**
   * 下载视频文件到本地
   */
  async downloadVideoFile(params) {
    try {
      showLoading('下载中...')
      
      // 构建文件名，确保以.mp4结尾
      let fileName = params.fileName || `video_${Date.now()}.mp4`
      
      // 确保文件名以.mp4结尾
      if (!fileName.toLowerCase().endsWith('.mp4')) {
        // 移除现有扩展名（如果有）
        const lastDotIndex = fileName.lastIndexOf('.')
        if (lastDotIndex > 0) {
          fileName = fileName.substring(0, lastDotIndex)
        }
        fileName += '.mp4'
      }
      
      // 清理文件名中的特殊字符
      fileName = fileName.replace(/[/\\:*?"<>|]/g, '_')
      
      const requestParams = {
        videoUrl: params.videoUrl,
        fileName: fileName,
        quality: params.quality
      }
      
      // 检查baseUrl是否存在
      if (!this.baseUrl) {
        console.error('baseUrl未定义')
        throw new Error('服务器地址未配置')
      }
      
      // #ifdef MP-WEIXIN
      try {
        // 微信小程序使用GET请求，通过URL参数传递
        const queryString = Object.keys(requestParams)
          .filter(key => requestParams[key] !== undefined && requestParams[key] !== null)
          .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(requestParams[key])}`)
          .join('&')
        
        const downloadUrl = `${this.baseUrl}/api/tools/media/download?${queryString}`
        console.log('下载请求URL:', downloadUrl)
        
        // 直接使用downloadFile API下载
        const downloadRes = await uni.downloadFile({
          url: downloadUrl,
          header: {
            'Accept': 'video/mp4, */*'
          }
        })
        
        console.log('下载结果:', downloadRes)
        
        if (downloadRes.statusCode === 200) {
          // 保存到相册
          await uni.saveVideoToPhotosAlbum({
            filePath: downloadRes.tempFilePath
          })
          
          uni.showToast({
            title: '已保存到相册',
            icon: 'success'
          })
          
          return {
            success: true,
            fileName: fileName
          }
        } else {
          throw new Error(`下载失败: ${downloadRes.statusCode}`)
        }
      } catch (error) {
        console.error('下载或保存失败:', error)
        // 如果是因为用户拒绝授权导致的失败
        if (error.errMsg && error.errMsg.includes('auth deny')) {
          uni.showModal({
            title: '授权提示',
            content: '需要授权保存到相册才能下载视频，是否去授权？',
            success: (res) => {
              if (res.confirm) {
                uni.openSetting()
              }
            }
          })
          throw new Error('需要授权保存到相册')
        }
        throw error
      }
      // #endif
      
      // #ifdef H5
      // H5端继续使用POST请求
      const downloadUrl = `${this.baseUrl}/api/tools/media/download`
      console.log('下载请求URL:', downloadUrl, '参数:', requestParams)
      
      // 调用下载接口
      const response = await uni.request({
        url: downloadUrl,
        method: 'POST',
        data: requestParams,
        responseType: 'arraybuffer',
        header: {
          'Content-Type': 'application/json',
          'Accept': 'video/mp4, */*'
        }
      })
      
      if (response.statusCode === 200) {
        this.triggerDownload(response.data, fileName)
        return {
          success: true,
          fileName: fileName
        }
      } else {
        console.error('下载失败，状态码:', response.statusCode)
        throw new Error(`下载失败: ${response.statusCode}`)
      }
      // #endif
      
    } catch (error) {
      hideLoading()
      console.error('视频下载失败:', error)
      throw error
    } finally {
      hideLoading()
    }
  }

  /**
   * H5端触发文件下载
   */
  triggerDownload(data, filename) {
    // #ifdef H5
    // 确保文件名以.mp4结尾
    if (!filename.toLowerCase().endsWith('.mp4')) {
      const lastDotIndex = filename.lastIndexOf('.')
      if (lastDotIndex > 0) {
        filename = filename.substring(0, lastDotIndex)
      }
      filename += '.mp4'
    }
    
    const blob = new Blob([data], { type: 'video/mp4' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.style.display = 'none'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    window.URL.revokeObjectURL(url)
    // #endif
  }

  /**
   * 音乐下载器（搜索）
   */
  async downloadMusic(params) {
    try {
      showLoading('搜索中...')
      const result = await this.callToolAPI('/api/tools/media/music-downloader', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }
  
  /**
   * 音乐文件下载
   */
  async downloadMusicFile(params) {
    try {
      showLoading('准备下载...')
      const result = await this.callToolAPI('/api/tools/media/music-download-file', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  // 获取音乐字节流（用于微信小程序直接下载）
  async downloadMusicStream(params) {
    try {
      showLoading('准备下载...')
      const result = await this.callToolAPI('/api/tools/media/download-music-stream', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }
  
  /**
   * 获取音乐预览URL
   */
  async getPreviewUrl(params) {
    try {
      showLoading('获取预览链接...')
      const result = await this.callToolAPI('/api/tools/media/get-preview-url', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 直接下载音乐文件到本地
   */
  async downloadMusicToLocal(filePath, fileName) {
    try {
      const downloadUrl = `${this.baseUrl}/api/tools/media/download-file?filePath=${encodeURIComponent(filePath)}&fileName=${encodeURIComponent(fileName)}`
      
      // #ifdef H5
      // H5端直接通过链接下载
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = fileName
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      return { success: true }
      // #endif
      
      // #ifdef MP-WEIXIN
      // 小程序端使用downloadFile
      const downloadRes = await uni.downloadFile({
        url: downloadUrl,
        header: {
          'Accept': 'audio/mpeg, */*'
        }
      })
      
      if (downloadRes.statusCode === 200) {
        // 小程序中无法直接保存到相册，提示用户
        uni.showModal({
          title: '下载完成',
          content: '文件已下载到临时目录，请注意及时保存',
          showCancel: false
        })
        return { success: true, tempFilePath: downloadRes.tempFilePath }
      } else {
        throw new Error(`下载失败: ${downloadRes.statusCode}`)
      }
      // #endif
      
    } catch (error) {
      console.error('下载失败:', error)
      throw error
    }
  }

  /**
   * 文案提取器
   */
  async extractText(imageUrl) {
    try {
      showLoading('识别中...')
      const result = await this.callToolAPI('/api/tools/media/text-extractor', 'POST', { imageUrl })
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 游戏语音合成
   */
  async synthesizeGameVoice(params) {
    try {
      showLoading('合成中...')
      const result = await this.callToolAPI('/api/tools/media/game-voice-synthesizer', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 音效大全
   */
  async getSoundEffects(category) {
    try {
      showLoading('加载中...')
      const result = await this.callToolAPI('/api/tools/media/sound-effects-library', 'GET', { category })
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 魔法抹除水印
   */
  async magicWatermarkRemover(imageUrl) {
    try {
      showLoading('处理中...')
      const result = await this.callToolAPI('/api/tools/media/magic-watermark-remover', 'POST', { imageUrl })
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 制作证件照
   */
  async makeIdPhoto(params) {
    try {
      console.log('开始制作证件照，参数:', params)
      
      // 使用专门的证件照API调用方法
      const result = await this.callIdPhotoAPI(params)
      
      console.log('证件照制作完成:', result)
      return result
    } catch (error) {
      console.error('证件照制作失败:', error)
      throw error
    }
  }

  /**
   * 上传证件照原图
   * @param {File} file - 图片文件
   * @returns {Promise} 上传结果
   */
  async uploadIdPhoto(file) {
    const formData = new FormData()
    formData.append('file', file)
    
    const result = await this.request({
      url: '/api/upload/idphoto',
      method: 'POST',
      data: formData,
      header: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000 // 60秒超时
    })
    
    return result
  }

  /**
   * 上传头像
   * @param {File} file - 图片文件
   * @param {string} userId - 用户ID（可选）
   * @returns {Promise} 上传结果
   */
  async uploadAvatar(file, userId = null) {
    const formData = new FormData()
    formData.append('file', file)
    if (userId) {
      formData.append('userId', userId)
    }
    
    const result = await this.request({
      url: '/api/upload/avatar',
      method: 'POST',
      data: formData,
      header: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000
    })
    
    return result
  }

  /**
   * 上传壁纸
   * @param {File} file - 图片文件
   * @param {string} category - 分类（可选）
   * @returns {Promise} 上传结果
   */
  async uploadWallpaper(file, category = null) {
    const formData = new FormData()
    formData.append('file', file)
    if (category) {
      formData.append('category', category)
    }
    
    const result = await this.request({
      url: '/api/upload/wallpaper',
      method: 'POST',
      data: formData,
      header: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000
    })
    
    return result
  }

  /**
   * 通用文件上传
   * @param {File} file - 文件
   * @param {string} type - 文件类型（可选）
   * @returns {Promise} 上传结果
   */
  async uploadCommon(file, type = null) {
    const formData = new FormData()
    formData.append('file', file)
    if (type) {
      formData.append('type', type)
    }
    
    const result = await this.request({
      url: '/api/upload/common',
      method: 'POST',
      data: formData,
      header: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000
    })
    
    return result
  }

  /**
   * 获取上传凭证
   * @param {string} type - 文件类型（可选）
   * @returns {Promise} 上传凭证
   */
  async getUploadToken(type = null) {
    const params = type ? { type } : {}
    
    const result = await this.request({
      url: '/api/upload/token',
      method: 'GET',
      data: params
    })
    
    return result
  }

  /**
   * 删除文件
   * @param {string} url - 文件URL
   * @returns {Promise} 删除结果
   */
  async deleteFile(url) {
    const result = await this.request({
      url: '/api/upload/delete',
      method: 'DELETE',
      data: { url }
    })
    
    return result
  }

  /**
   * 专门用于证件照制作的API调用（支持超时设置）
   * @param {Object} params - 请求参数
   * @returns {Promise} 处理结果
   */
  async callIdPhotoAPI(params) {
    return new Promise((resolve, reject) => {
      uni.request({
        url: this.baseUrl + '/api/tools/media/id-photo-maker',
        method: 'POST',
        data: params,
        header: {
          'Content-Type': 'application/json'
        },
        timeout: 180000, // 3分钟超时
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`))
          }
        },
        fail: (err) => {
          reject(err)
        }
      })
    })
  }

  // ============ 实用工具 ============

  /**
   * 车辆价格查询
   */
  async queryVehiclePrice(params) {
    try {
      showLoading('查询中...')
      const result = await this.callToolAPI('/api/tools/utility/vehicle-price-query', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 全国油价查询
   */
  async queryGasPrice(params) {
    try {
      showLoading('查询中...')
      const result = await this.callToolAPI('/api/tools/utility/gas-price-query', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 全国天气查询
   */
  async queryWeather(params) {
    try {
      showLoading('查询中...')
      const result = await this.callToolAPI('/api/tools/utility/weather-query', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  // ============ 趣味工具 ============

  /**
   * 兽语加密解密
   */
  async beastLanguage(params) {
    try {
      showLoading('处理中...')
      const result = await this.callToolAPI('/api/tools/fun/beast-language', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 历史上的今天
   */
  async getHistoryToday(params = {}) {
    try {
      showLoading('查询历史事件...')
      const result = await this.callToolAPI('/api/tools/fun/history-today', 'POST', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 随机头像
   */
  async getRandomAvatars(params = {}) {
    try {
      showLoading('生成头像中...')
      
      // 转换参数
      const gender = params.gender === 'male' ? 'boy' : 
                    params.gender === 'female' ? 'girl' : 'boy'
      
      // 女生头像风格类型处理
      let girlType = 1 // 默认酷girl
      if (params.gender === 'female' && params.girlType) {
        girlType = params.girlType
      } else {
        // 根据通用风格映射到女生风格
        girlType = params.style === 'cartoon' ? 2 : 
                  params.style === 'realistic' ? 1 :
                  params.style === 'pixel' ? 4 : 1
      }
      
      // 手动构建查询参数字符串
      const queryParams = `gender=${gender}&girlType=${girlType}&returnType=json`
      
      const result = await this.callToolAPI(`/api/tools/wallpaper/random-avatar?${queryParams}`, 'GET')
      hideLoading()
      
      // 处理返回结果，生成多个头像
      if (result.code === 200 && result.data && result.data.success) {
        const avatars = []
        const count = params.count || 3  // 改为3个头像
        
        // 生成多个不同的头像
        for (let i = 0; i < count; i++) {
          const singleResult = await this.callToolAPI(`/api/tools/wallpaper/random-avatar?${queryParams}`, 'GET')
          if (singleResult.code === 200 && singleResult.data && singleResult.data.avatar) {
            avatars.push(singleResult.data.avatar)
          }
          // 添加延迟避免过于频繁的请求
          if (i < count - 1) {
            await new Promise(resolve => setTimeout(resolve, 200))  // 增加延迟到200ms
          }
        }
        
        return {
          success: true,
          data: {
            avatars: avatars,
            gender: result.data.gender,
            type: result.data.type
          },
          message: '生成成功'
        }
      } else {
        throw new Error(result.message || '生成失败')
      }
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 显卡天梯图
   */
  async getGpuLadder() {
    try {
      showLoading('加载中...')
      const result = await this.callToolAPI('/api/tools/fun/gpu-ladder', 'GET')
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * CPU天梯图
   */
  async getCpuLadder() {
    try {
      showLoading('加载中...')
      const result = await this.callToolAPI('/api/tools/fun/cpu-ladder', 'GET')
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  // ============ 程序员工具 ============

  /**
   * 生成网站快照
   */
  async generateWebsiteSnapshot(params) {
    try {
      showLoading('生成中...')

      // 构建请求参数
      const requestParams = {
        url: params.url,
        device: params.device || 'desktop',
        format: params.format || 'png',
        quality: params.quality || 90,
        delay: params.delay || 2,
        fullPage: params.fullPage || false,
        removeAds: params.removeAds || false
      }

      console.log('发送网站快照请求:', requestParams)
      const result = await this.callToolAPI('/api/tools/dev/website-snapshot', 'POST', requestParams)
      console.log('网站快照返回结果:', result)

      hideLoading()
      return result
    } catch (error) {
      console.error('网站快照生成失败:', error)
      hideLoading()
      throw error
    }
  }

  // ============ 图片壁纸工具 ============

  /**
   * 随机手机壁纸
   */
  async getRandomMobileWallpaper(params) {
    try {
      showLoading('加载中...')
      const result = await this.callToolAPI('/api/tools/wallpaper/random-mobile-wallpaper', 'GET', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 获取随机头像
   */
  async getRandomAvatar(params) {
    try {
      showLoading('获取中...')
      const result = await this.callToolAPI('/api/tools/wallpaper/random-avatar', 'GET', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 获取随机加载图
   */
  async getRandomLoadingImage(params) {
    try {
      showLoading('获取中...')
      const result = await this.callToolAPI('/api/tools/wallpaper/random-loading-image', 'GET', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }



  /**
   * 随机PC壁纸
   */
  async getRandomPcWallpaper(params) {
    try {
      showLoading('获取中...')
      const result = await this.callToolAPI('/api/tools/wallpaper/random-pc-wallpaper', 'GET', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  /**
   * 动漫壁纸
   */
  async getAnimeWallpapers(params) {
    try {
      showLoading('获取中...')
      const result = await this.callToolAPI('/api/tools/wallpaper/anime-wallpapers', 'GET', params)
      hideLoading()
      return result
    } catch (error) {
      hideLoading()
      throw error
    }
  }

  // ============ 通用API调用方法 ============

  /**
   * 调用工具API
   */
  async callToolAPI(url, method = 'GET', params = {}) {
    try {
      const requestUrl = this.baseUrl + url
      console.log(`发起${method}请求:`, requestUrl, params)

      // 根据不同的API设置不同的超时时间
      let timeout = 10000 // 默认10秒
      let maxRetries = 1 // 默认重试1次

      if (url.includes('id-photo-maker')) {
        timeout = 120000 // 证件照制作设置120秒超时
        maxRetries = 0 // 证件照制作不重试，避免重复处理
      } else if (url.includes('website-snapshot')) {
        timeout = 120000 // 网站快照设置120秒超时
        maxRetries = 0 // 网站快照不重试，避免重复处理
      } else if (url.includes('video') || url.includes('image')) {
        timeout = 60000 // 其他媒体处理类API设置60秒超时
      }

      // 重试机制
      for (let attempt = 0; attempt <= maxRetries; attempt++) {
        try {
          const response = await uni.request({
            url: requestUrl,
            method: method,
            data: method === 'GET' ? params : JSON.stringify(params),
            header: {
              'Content-Type': 'application/json'
            },
            timeout: timeout
          })

          console.log('API响应:', response)

          // 检查响应状态
          if (response.statusCode !== 200) {
            throw new Error(`请求失败: ${response.statusCode}`)
          }

          // 检查响应数据
          const result = response.data
          if (!result) {
            throw new Error('响应数据为空')
          }

          return result
        } catch (error) {
          console.log(`第${attempt + 1}次请求失败:`, error)
          
          // 如果是最后一次尝试，抛出错误
          if (attempt === maxRetries) {
            throw error
          }
          
          // 等待1秒后重试
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }
    } catch (error) {
      console.error('API调用失败:', error)
      throw error
    }
  }

  // ============ 文件处理方法 ============

  /**
   * 选择并上传图片
   */
  async selectAndUploadImage() {
    return new Promise((resolve, reject) => {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          // 这里可以添加上传逻辑
          resolve({
            path: tempFilePath,
            size: res.tempFiles[0].size
          })
        },
        fail: (err) => {
          reject(new Error('选择图片失败'))
        }
      })
    })
  }

  /**
   * 选择并上传音频
   */
  async selectAndUploadAudio() {
    return new Promise((resolve, reject) => {
      uni.chooseFile({
        count: 1,
        type: 'file',
        extension: ['mp3', 'wav', 'aac'],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0]
          resolve({
            path: tempFilePath,
            size: res.tempFiles[0].size
          })
        },
        fail: (err) => {
          reject(new Error('选择音频失败'))
        }
      })
    })
  }

  /**
   * 选择并上传视频
   */
  async selectAndUploadVideo() {
    return new Promise((resolve, reject) => {
      uni.chooseVideo({
        sourceType: ['album', 'camera'],
        maxDuration: 60,
        camera: 'back',
        success: (res) => {
          resolve({
            path: res.tempFilePath,
            duration: res.duration,
            size: res.size
          })
        },
        fail: (err) => {
          reject(new Error('选择视频失败'))
        }
      })
    })
  }

  /**
   * 下载文件
   */
  async downloadFile(url, filename) {
    return new Promise((resolve, reject) => {
      uni.downloadFile({
        url: url,
        success: (res) => {
          if (res.statusCode === 200) {
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                resolve(res.tempFilePath)
              },
              fail: (err) => {
                reject(new Error('保存失败'))
              }
            })
          } else {
            reject(new Error('下载失败'))
          }
        },
        fail: (err) => {
          reject(new Error('下载失败'))
        }
      })
    })
  }

  /**
   * 预览图片
   */
  previewImage(urls, current = 0) {
    uni.previewImage({
      urls: Array.isArray(urls) ? urls : [urls],
      current: current
    })
  }

  /**
   * 判断是否为图片URL
   */
  isImageUrl(url) {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
    return imageExtensions.some(ext => url.toLowerCase().includes(ext))
  }

  /**
   * 复制URL到剪贴板
   */
  async copyUrl(url) {
    return new Promise((resolve, reject) => {
      uni.setClipboardData({
        data: url,
        success: () => {
          showSuccess('链接已复制到剪贴板')
          resolve()
        },
        fail: (err) => {
          reject(new Error('复制失败'))
        }
      })
    })
  }

  /**
   * 分享内容
   */
  async shareContent(title, url, imageUrl) {
    return new Promise((resolve, reject) => {
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: url,
        title: title,
        summary: title,
        imageUrl: imageUrl,
        success: () => {
          resolve()
        },
        fail: (err) => {
          reject(new Error('分享失败'))
        }
      })
    })
  }

  /**
   * 获取工具配置
   */
  getToolConfig(toolIdentifier) {
    // 工具配置映射
    const toolConfigs = {
      'fun-image-generator': {
        name: '趣味图片生成器',
        icon: '🎨',
        needsVip: false,
        maxFileSize: 10 * 1024 * 1024, // 10MB
        supportedFormats: ['jpg', 'png', 'gif']
      },
      'get-vip-membership': {
        name: '获取永久会员',
        icon: '👑',
        needsVip: false,
        description: '获取永久会员权益'
      },
      'video-watermark-remover': {
        name: '视频去水印',
        icon: '🎬',
        needsVip: true,
        maxFileSize: 100 * 1024 * 1024, // 100MB
        supportedFormats: ['mp4', 'avi', 'mov']
      },
      // 可以继续添加其他工具的配置...
    }

    return toolConfigs[toolIdentifier] || {}
  }
}

// 创建并导出单例实例
export const toolService = new ToolService();

// 为了向后兼容，保持默认导出
export default toolService; 