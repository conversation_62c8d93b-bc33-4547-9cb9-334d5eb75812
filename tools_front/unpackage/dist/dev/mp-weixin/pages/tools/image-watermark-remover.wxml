<view class="min-h-screen bg-gray-50 data-v-920616e1"><view class="p-4 space-y-4 data-v-920616e1"><view class="bg-white rounded-lg shadow-sm p-4 data-v-920616e1"><view class="font-semibold text-gray-900 mb-4 flex items-center data-v-920616e1"><upload wx:if="{{a}}" class="mr-2 text-blue-500 data-v-920616e1" u-i="920616e1-0" bind:__l="__l" u-p="{{a}}"/> 上传图片文件 </view><view wx:if="{{b}}" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center data-v-920616e1"><image-icon wx:if="{{c}}" class="mx-auto text-gray-400 mb-4 data-v-920616e1" u-i="920616e1-1" bind:__l="__l" u-p="{{c}}"/><view class="text-gray-600 mb-4 data-v-920616e1">点击选择图片文件</view><button bindtap="{{d}}" class="bg-blue-500 text-white px-6 py-2 rounded-lg cursor-pointer hover-bg-blue-600 transition-colors data-v-920616e1"> 选择图片文件 </button><view class="text-sm text-gray-500 mt-2 data-v-920616e1"> 支持 JPG, PNG, GIF, WEBP 等格式，可批量选择 </view></view><view wx:else class="space-y-4 data-v-920616e1"><view class="flex items-center justify-between data-v-920616e1"><view class="text-sm text-gray-600 data-v-920616e1">已选择 {{e}} 张图片</view><view class="flex items-center gap-2 data-v-920616e1"><button bindtap="{{g}}" class="{{['data-v-920616e1', 'px-3 py-1 rounded-md text-sm transition-colors', h]}}">{{f}}</button><button bindtap="{{j}}" class="px-3 py-1 border border-gray-300 rounded-md hover-bg-gray-50 flex items-center data-v-920616e1"><rotate-ccw wx:if="{{i}}" class="data-v-920616e1" u-i="920616e1-2" bind:__l="__l" u-p="{{i}}"/></button></view></view><view wx:if="{{k}}" class="bg-blue-50 border border-blue-200 rounded-lg p-3 data-v-920616e1"><view class="text-sm text-blue-800 data-v-920616e1"><view class="data-v-920616e1">水印区域选择模式：</view>点击图片可以拖拽选择要去除的水印区域 </view></view><view class="grid grid-cols-2 gap-3 max-h-80 overflow-y-auto data-v-920616e1"><view wx:for="{{l}}" wx:for-item="url" wx:key="j" class="relative data-v-920616e1"><view class="relative w-full h-32 rounded-lg overflow-hidden data-v-920616e1" bindtap="{{url.e}}"><image src="{{url.a}}" alt="{{url.b}}" class="{{['w-full', 'h-full', 'object-cover', 'data-v-920616e1', m && 'cursor-pointer']}}"/><view wx:if="{{url.c}}" class="absolute border-2 border-red-500 bg-red-500 bg-opacity-20 data-v-920616e1" style="{{url.d}}"><view class="absolute -top-6 left-0 bg-red-500 text-white text-xs px-2 py-1 rounded data-v-920616e1"> 水印区域 </view></view></view><button wx:if="{{n}}" bindtap="{{url.h}}" class="absolute top-1 right-1 bg-red-500 text-white p-1 rounded-full hover-bg-red-600 data-v-920616e1"><trash2 wx:if="{{url.g}}" class="data-v-920616e1" u-i="{{url.f}}" bind:__l="__l" u-p="{{url.g}}"/></button><view class="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded data-v-920616e1">{{url.i}}</view></view><view bindtap="{{p}}" class="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center hover:bg-gray-50 transition-colors cursor-pointer data-v-920616e1"><plus wx:if="{{o}}" class="text-gray-400 mb-2 data-v-920616e1" u-i="920616e1-4" bind:__l="__l" u-p="{{o}}"/><text class="text-sm text-gray-500 data-v-920616e1">添加</text></view></view></view></view><button wx:if="{{q}}" bindtap="{{s}}" disabled="{{t}}" class="w-full py-6 text-lg font-semibold bg-blue-600 text-white rounded-md hover-bg-blue-700 disabled-bg-gray-400 disabled-cursor-not-allowed data-v-920616e1">{{r}}</button><view wx:if="{{v}}" class="bg-white rounded-2xl p-4 data-v-920616e1"><view class="flex items-center justify-between mb-2 data-v-920616e1"><label class="text-sm font-medium data-v-920616e1">处理进度</label><label class="text-sm text-gray-600 data-v-920616e1">{{w}}%</label></view><view class="w-full bg-gray-200 rounded-full h-2 data-v-920616e1"><view class="bg-blue-500 h-2 rounded-full transition-all duration-300 data-v-920616e1" style="{{'width:' + x}}"/></view></view><view wx:if="{{y}}" class="bg-white rounded-2xl p-4 data-v-920616e1"><view class="font-semibold text-gray-900 mb-3 data-v-920616e1">处理完成</view><view class="grid grid-cols-2 gap-3 max-h-80 overflow-y-auto mb-4 data-v-920616e1"><view wx:for="{{z}}" wx:for-item="url" wx:key="e" class="relative group cursor-pointer data-v-920616e1" bindtap="{{url.f}}"><image src="{{url.a}}" alt="{{url.b}}" class="w-full h-32 object-cover rounded-lg data-v-920616e1"/><view class="absolute top-1 left-1 bg-green-500 text-white text-xs px-2 py-1 rounded data-v-920616e1"> 已处理 </view><view class="absolute inset-0 bg-black bg-opacity-0 group-hover-bg-opacity-40 transition-all duration-300 rounded-lg flex items-center justify-center data-v-920616e1"><view class="opacity-0 group-hover-opacity-100 transform translate-y-2 group-hover-translate-y-0 transition-all duration-300 flex flex-col items-center gap-2 data-v-920616e1"><button catchtap="{{url.d}}" class="bg-blue-500 text-white px-4 py-2 rounded-lg flex items-center shadow-lg hover:bg-blue-600 active:scale-95 data-v-920616e1"><download wx:if="{{A}}" class="mr-2 data-v-920616e1" u-i="{{url.c}}" bind:__l="__l" u-p="{{A}}"/><text class="data-v-920616e1">下载</text></button></view></view></view></view><view class="flex gap-3 data-v-920616e1"><button bindtap="{{C}}" class="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover-bg-blue-700 flex items-center justify-center data-v-920616e1"><download wx:if="{{B}}" class="mr-2 data-v-920616e1" u-i="920616e1-6" bind:__l="__l" u-p="{{B}}"/> 打包下载全部 </button><button bindtap="{{E}}" class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover-bg-gray-50 flex items-center justify-center data-v-920616e1"><rotate-ccw wx:if="{{D}}" class="mr-2 data-v-920616e1" u-i="920616e1-7" bind:__l="__l" u-p="{{D}}"/> 重新处理 </button></view></view></view></view><view wx:if="{{F}}" class="preview-modal data-v-920616e1"><view class="preview-content data-v-920616e1" catchtap="{{J}}"><image src="{{G}}" class="preview-image data-v-920616e1" mode="aspectFit"/><view class="close-btn data-v-920616e1" bindtap="{{H}}"><text class="close-icon data-v-920616e1">×</text></view><view class="download-btn data-v-920616e1" bindtap="{{I}}"><text class="download-text data-v-920616e1">下载此图片</text></view></view><view class="preview-mask data-v-920616e1" bindtap="{{K}}"></view></view><view wx:if="{{L}}" class="watermark-selector-modal data-v-920616e1"><view class="watermark-selector-content data-v-920616e1" catchtap="{{W}}"><view class="selector-header data-v-920616e1"><text class="selector-title data-v-920616e1">选择水印区域</text><view class="selector-actions data-v-920616e1"><button bindtap="{{M}}" class="action-btn clear-btn data-v-920616e1">清除</button><button bindtap="{{N}}" class="action-btn confirm-btn data-v-920616e1">确认</button><button bindtap="{{O}}" class="action-btn close-btn data-v-920616e1">×</button></view></view><view class="image-container data-v-920616e1" ref="imageContainer"><image src="{{P}}" class="selector-image data-v-920616e1" mode="aspectFit" bindload="{{Q}}" bindtouchstart="{{R}}" bindtouchmove="{{S}}" bindtouchend="{{T}}"/><view wx:if="{{U}}" class="selection-box data-v-920616e1" style="{{V}}"><view class="selection-border data-v-920616e1"></view><text class="selection-label data-v-920616e1">水印区域</text></view></view><view class="selector-tips data-v-920616e1"><text class="tip-text data-v-920616e1">拖拽选择要去除的水印区域，松开手指完成选择</text></view></view><view class="watermark-selector-mask data-v-920616e1" bindtap="{{X}}"></view></view>