"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_toolService = require("../../utils/toolService.js");
const utils_index = require("../../utils/index.js");
const _sfc_main = {
  __name: "bing-daily-wallpaper",
  setup(__props) {
    const toolService = new utils_toolService.ToolService();
    common_vendor.ref(false);
    const batchLoading = common_vendor.ref(false);
    const imageLoaded = common_vendor.ref(true);
    const imageError = common_vendor.ref(false);
    const selectedCategory = common_vendor.ref({ id: "nature", name: "自然风光" });
    const selectedResolution = common_vendor.ref({ id: "750x1334", name: "iPhone 6/7/8", width: 750, height: 1334 });
    const categories = [
      { id: "all", name: "全部" },
      { id: "nature", name: "自然风光" },
      { id: "city", name: "城市建筑" },
      { id: "abstract", name: "抽象艺术" },
      { id: "space", name: "宇宙星空" },
      { id: "animal", name: "动物世界" },
      { id: "tech", name: "科技数码" },
      { id: "art", name: "艺术创作" },
      { id: "minimal", name: "简约风格" }
    ];
    const wallpaperBatch = common_vendor.ref([]);
    const favorites = common_vendor.ref([]);
    const downloadHistory = common_vendor.ref([]);
    const selectCategory = async (category) => {
      selectedCategory.value = category;
      await generateWallpaperBatch(category.id);
    };
    const generateWallpaperBatch = async (categoryId = "all") => {
      batchLoading.value = true;
      try {
        const result = await toolService.getRandomMobileWallpaper({
          category: categoryId,
          resolution: selectedResolution.value,
          count: 6
        });
        if (result.success && result.data && result.data.length > 0) {
          wallpaperBatch.value = result.data.map((wallpaper, index) => ({
            id: wallpaper.id || Date.now() + index,
            title: wallpaper.title || `${selectedCategory.value.name} ${index + 1}`,
            description: wallpaper.description || `精美的${selectedCategory.value.name}壁纸`,
            url: wallpaper.url,
            category: selectedCategory.value.name,
            tags: wallpaper.tags || [selectedCategory.value.name, "高清", "精美"]
          }));
          utils_index.showSuccess("壁纸批次加载成功！");
        } else {
          throw new Error(result.message || "获取壁纸批次失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/bing-daily-wallpaper.vue:150", "获取壁纸批次失败:", error);
        try {
          await simulateBatchGeneration(categoryId);
          utils_index.showSuccess("壁纸批次加载成功！（本地处理）");
        } catch (localError) {
          utils_index.showError("壁纸批次加载失败，请重试");
        }
      } finally {
        batchLoading.value = false;
      }
    };
    const simulateBatchGeneration = async (categoryId = "all") => {
      return new Promise((resolve) => {
        setTimeout(() => {
          const batch = [];
          const batchSize = 6;
          for (let i = 0; i < batchSize; i++) {
            const randomId = Date.now() + i;
            let imageUrl = `https://picsum.photos/${selectedResolution.value.width}/${selectedResolution.value.height}?random=${randomId}`;
            if (categoryId === "abstract") {
              imageUrl += "&blur=1";
            } else if (categoryId === "minimal") {
              imageUrl += "&grayscale";
            }
            batch.push({
              id: randomId,
              title: `${selectedCategory.value.name} ${i + 1}`,
              description: `精美的${selectedCategory.value.name}壁纸`,
              url: imageUrl,
              category: selectedCategory.value.name,
              tags: [selectedCategory.value.name, "高清", "精美"]
            });
          }
          wallpaperBatch.value = batch;
          resolve();
        }, 1e3);
      });
    };
    const toggleFavorite = (id) => {
      if (favorites.value.includes(id)) {
        favorites.value = favorites.value.filter((fav) => fav !== id);
      } else {
        favorites.value.push(id);
      }
    };
    const downloadWallpaper = (wallpaper) => {
      common_vendor.index.showLoading({
        title: "下载中..."
      });
      common_vendor.index.downloadFile({
        url: wallpaper.url,
        success: (res) => {
          if (res.statusCode === 200) {
            common_vendor.index.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "保存成功",
                  icon: "success",
                  duration: 2e3
                });
              },
              fail: (error) => {
                common_vendor.index.hideLoading();
                common_vendor.index.__f__("error", "at pages/tools/bing-daily-wallpaper.vue:226", "保存失败:", error);
                common_vendor.index.showToast({
                  title: "保存失败",
                  icon: "error",
                  duration: 2e3
                });
              }
            });
          }
        },
        fail: (error) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/tools/bing-daily-wallpaper.vue:238", "下载失败:", error);
          common_vendor.index.showToast({
            title: "下载失败",
            icon: "error",
            duration: 2e3
          });
        }
      });
      const downloadItem = {
        ...wallpaper,
        downloadTime: (/* @__PURE__ */ new Date()).toLocaleString()
      };
      downloadHistory.value.unshift(downloadItem);
      if (downloadHistory.value.length > 20) {
        downloadHistory.value = downloadHistory.value.slice(0, 20);
      }
    };
    const getCategoryIcon = (categoryId) => {
      const icons = {
        "all": "🖼️",
        "nature": "🌲",
        "city": "🏙️",
        "abstract": "🎨",
        "space": "🌌",
        "animal": "🐾",
        "tech": "💻",
        "art": "🎭",
        "minimal": "⚪"
      };
      return icons[categoryId] || "🖼️";
    };
    common_vendor.onMounted(() => {
      selectedCategory.value = categories[0];
      generateWallpaperBatch("all");
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.f(categories, (category, k0, i0) => {
          return {
            a: common_vendor.t(getCategoryIcon(category.id)),
            b: common_vendor.t(category.name),
            c: category.id,
            d: selectedCategory.value.id === category.id ? 1 : "",
            e: common_vendor.o(($event) => selectCategory(category), category.id)
          };
        }),
        b: common_vendor.t(selectedCategory.value.name || "全部壁纸"),
        c: common_vendor.t(wallpaperBatch.value.length),
        d: common_vendor.f(wallpaperBatch.value, (wallpaper, k0, i0) => {
          return {
            a: wallpaper.url,
            b: wallpaper.title,
            c: common_vendor.o(($event) => imageLoaded.value = true, wallpaper.id),
            d: common_vendor.o(($event) => imageError.value = true, wallpaper.id),
            e: common_vendor.t(wallpaper.title),
            f: common_vendor.f(wallpaper.tags.slice(0, 3), (tag, index, i1) => {
              return {
                a: common_vendor.t(tag),
                b: index
              };
            }),
            g: favorites.value.includes(wallpaper.id) ? 1 : "",
            h: common_vendor.o(($event) => toggleFavorite(wallpaper.id), wallpaper.id),
            i: common_vendor.o(($event) => downloadWallpaper(wallpaper), wallpaper.id),
            j: wallpaper.id
          };
        }),
        e: common_vendor.t(selectedResolution.value.width),
        f: common_vendor.t(selectedResolution.value.height)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b1ea4072"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/bing-daily-wallpaper.js.map
