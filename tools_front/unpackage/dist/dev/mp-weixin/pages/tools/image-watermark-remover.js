"use strict";
const common_vendor = require("../../common/vendor.js");
const config_api = require("../../config/api.js");
const _sfc_main = {
  name: "ImageWatermarkRemover",
  components: {
    Upload: common_vendor.Upload,
    Download: common_vendor.Download,
    ImageIcon: common_vendor.Image,
    RotateCcw: common_vendor.RotateCcw,
    Trash2: common_vendor.Trash2,
    Plus: common_vendor.Plus
  },
  data() {
    return {
      images: [],
      imageUrls: [],
      isProcessing: false,
      processedImages: [],
      progress: 0,
      showPreview: false,
      currentPreviewImage: "",
      // 水印区域选择相关
      isWatermarkSelectionMode: false,
      showWatermarkSelector: false,
      currentSelectorImage: "",
      currentSelectorIndex: -1,
      watermarkAreas: {},
      // 存储每张图片的水印区域 {imageIndex: {x, y, width, height}}
      // 选择框相关
      selectionBox: {
        visible: false,
        startX: 0,
        startY: 0,
        endX: 0,
        endY: 0
      },
      imageInfo: {
        width: 0,
        height: 0,
        displayWidth: 0,
        displayHeight: 0
      },
      isDragging: false
    };
  },
  computed: {
    selectionBoxStyle() {
      if (!this.selectionBox.visible)
        return {};
      const left = Math.min(this.selectionBox.startX, this.selectionBox.endX);
      const top = Math.min(this.selectionBox.startY, this.selectionBox.endY);
      const width = Math.abs(this.selectionBox.endX - this.selectionBox.startX);
      const height = Math.abs(this.selectionBox.endY - this.selectionBox.startY);
      return {
        left: `${left}px`,
        top: `${top}px`,
        width: `${width}px`,
        height: `${height}px`
      };
    }
  },
  methods: {
    handleChooseImage() {
      common_vendor.index.chooseImage({
        count: 9 - this.images.length,
        // 最多可选择的数量为9减去已有图片数
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          const tempFiles = res.tempFiles.map((file) => ({
            ...file,
            path: file.path || file.tempFilePath
          }));
          this.images = [...this.images, ...tempFiles];
          this.imageUrls = [...this.imageUrls, ...tempFilePaths];
          this.processedImages = [];
          this.progress = 0;
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:335", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    async handleProcess() {
      if (this.images.length === 0)
        return;
      this.isProcessing = true;
      this.progress = 0;
      try {
        const formData = new FormData();
        this.images.forEach((image, index) => {
          formData.append("files", image);
        });
        formData.append("algorithm", "lama");
        formData.append("outputFormat", "png");
        formData.append("quality", "high");
        const hasWatermarkAreas = Object.keys(this.watermarkAreas).length > 0;
        formData.append("autoDetect", hasWatermarkAreas ? "false" : "true");
        formData.append("preserveDetails", "true");
        if (hasWatermarkAreas) {
          const watermarkAreasArray = [];
          Object.keys(this.watermarkAreas).forEach((imageIndex) => {
            const area = this.watermarkAreas[imageIndex];
            watermarkAreasArray.push({
              imageIndex: parseInt(imageIndex),
              // 使用原图坐标
              x: area.originalX || area.x,
              y: area.originalY || area.y,
              width: area.originalWidth || area.width,
              height: area.originalHeight || area.height
            });
          });
          formData.append("watermarkAreas", JSON.stringify(watermarkAreasArray));
          common_vendor.index.__f__("log", "at pages/tools/image-watermark-remover.vue:408", "发送到后端的水印区域数据:", watermarkAreasArray);
        }
        const responseData = await config_api.httpRequest.postFormData(
          config_api.API_ENDPOINTS.IMAGE_WATERMARK_REMOVAL.BATCH,
          formData
        );
        if (responseData && responseData.success) {
          const taskId = responseData.data.taskId;
          await this.pollProgress(taskId);
        } else {
          throw new Error((responseData == null ? void 0 : responseData.message) || "处理失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:427", "图片处理失败:", error);
        this.isProcessing = false;
        common_vendor.index.showToast({
          title: "处理失败: " + (error.message || "未知错误"),
          icon: "none"
        });
      }
    },
    async pollProgress(taskId) {
      const pollInterval = setInterval(async () => {
        try {
          const progressResult = await config_api.httpRequest.get(
            `${config_api.API_ENDPOINTS.IMAGE_WATERMARK_REMOVAL.PROGRESS}/${taskId}`
          );
          if (progressResult && progressResult.success) {
            const progressData = progressResult.data;
            this.progress = progressData.progress || 0;
            if (progressData.status === "COMPLETED") {
              clearInterval(pollInterval);
              await this.getProcessResult(taskId);
            } else if (progressData.status === "FAILED") {
              clearInterval(pollInterval);
              throw new Error("处理失败");
            }
          }
        } catch (error) {
          clearInterval(pollInterval);
          common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:457", "获取进度失败:", error);
          this.isProcessing = false;
          common_vendor.index.showToast({
            title: "获取进度失败",
            icon: "none"
          });
        }
      }, 1e3);
    },
    async getProcessResult(taskId) {
      try {
        const resultResult = await config_api.httpRequest.get(
          `${config_api.API_ENDPOINTS.IMAGE_WATERMARK_REMOVAL.RESULT}/${taskId}`
        );
        if (resultResult && resultResult.success) {
          const resultData = resultResult.data;
          if (resultData.results && resultData.results.length > 0) {
            this.processedImages = resultData.results.filter((result) => result.status === "SUCCESS").map((result) => result.processedUrl);
          }
          this.isProcessing = false;
          this.progress = 100;
          common_vendor.index.showToast({
            title: `处理完成！成功: ${resultData.successCount}, 失败: ${resultData.failedCount}`,
            icon: "success"
          });
        } else {
          throw new Error("获取结果失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:493", "获取结果失败:", error);
        this.isProcessing = false;
        common_vendor.index.showToast({
          title: "获取结果失败",
          icon: "none"
        });
      }
    },
    async handleDownloadAll() {
      if (this.processedImages.length === 0) {
        common_vendor.index.showToast({
          title: "没有可下载的图片",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "正在保存...",
          mask: true
        });
        const auth = await common_vendor.index.authorize({
          scope: "scope.writePhotosAlbum"
        }).catch(() => null);
        if (!auth) {
          common_vendor.index.hideLoading();
          const res = await common_vendor.index.showModal({
            title: "提示",
            content: "需要保存到相册的权限，是否前往设置？",
            confirmText: "前往设置"
          });
          if (res.confirm) {
            await common_vendor.index.openSetting();
          }
          return;
        }
        let savedCount = 0;
        for (const imageUrl of this.processedImages) {
          try {
            await common_vendor.index.saveImageToPhotosAlbum({
              filePath: imageUrl
            });
            savedCount++;
          } catch (err) {
            common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:547", "保存图片失败:", err);
          }
        }
        common_vendor.index.hideLoading();
        if (savedCount > 0) {
          common_vendor.index.showToast({
            title: `成功保存${savedCount}张图片`,
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "error"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:590", "下载失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存失败",
          icon: "error"
        });
      }
    },
    handleReset() {
      this.images = [];
      this.imageUrls = [];
      this.processedImages = [];
      this.progress = 0;
      this.isProcessing = false;
    },
    removeImage(index) {
      this.images = this.images.filter((_, i) => i !== index);
      this.imageUrls = this.imageUrls.filter((_, i) => i !== index);
    },
    getFileName(file) {
      if (!file)
        return "未知文件";
      const path = file.path || "";
      const name = path.split("/").pop() || "未知文件";
      return name.length > 15 ? name.substring(0, 15) + "..." : name;
    },
    async handleDownloadSingle(imageUrl, index) {
      try {
        common_vendor.index.showLoading({
          title: "正在保存...",
          mask: true
        });
        const auth = await common_vendor.index.authorize({
          scope: "scope.writePhotosAlbum"
        }).catch(() => null);
        if (!auth) {
          common_vendor.index.hideLoading();
          const res = await common_vendor.index.showModal({
            title: "提示",
            content: "需要保存到相册的权限，是否前往设置？",
            confirmText: "前往设置"
          });
          if (res.confirm) {
            await common_vendor.index.openSetting();
          }
          return;
        }
        await common_vendor.index.saveImageToPhotosAlbum({
          filePath: imageUrl
        });
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "已保存到相册",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:688", "下载失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存失败",
          icon: "error"
        });
      }
    },
    // 添加预览方法
    handlePreview(url) {
      common_vendor.index.__f__("log", "at pages/tools/image-watermark-remover.vue:698", "预览图片:", url);
      common_vendor.index.__f__("log", "at pages/tools/image-watermark-remover.vue:699", "当前showPreview状态:", this.showPreview);
      this.showPreview = true;
      this.currentPreviewImage = url;
      common_vendor.index.__f__("log", "at pages/tools/image-watermark-remover.vue:702", "设置后showPreview状态:", this.showPreview);
      common_vendor.index.__f__("log", "at pages/tools/image-watermark-remover.vue:703", "设置后currentPreviewImage:", this.currentPreviewImage);
    },
    closePreview() {
      this.showPreview = false;
      this.currentPreviewImage = "";
    },
    // 水印区域选择相关方法
    toggleWatermarkSelection() {
      this.isWatermarkSelectionMode = !this.isWatermarkSelectionMode;
      if (!this.isWatermarkSelectionMode) {
        this.watermarkAreas = {};
      }
    },
    openWatermarkSelector(imageIndex) {
      this.currentSelectorIndex = imageIndex;
      this.currentSelectorImage = this.imageUrls[imageIndex];
      this.showWatermarkSelector = true;
      this.selectionBox.visible = false;
      this.isDragging = false;
    },
    closeWatermarkSelector() {
      this.showWatermarkSelector = false;
      this.currentSelectorIndex = -1;
      this.currentSelectorImage = "";
      this.selectionBox.visible = false;
      this.isDragging = false;
    },
    confirmWatermarkArea() {
      if (this.selectionBox.visible && this.currentSelectorIndex >= 0) {
        const left = Math.min(this.selectionBox.startX, this.selectionBox.endX);
        const top = Math.min(this.selectionBox.startY, this.selectionBox.endY);
        const width = Math.abs(this.selectionBox.endX - this.selectionBox.startX);
        const height = Math.abs(this.selectionBox.endY - this.selectionBox.startY);
        const originalX = Math.round(left);
        const originalY = Math.round(top);
        const originalWidth = Math.round(width);
        const originalHeight = Math.round(height);
        this.$set(this.watermarkAreas, this.currentSelectorIndex, {
          // 显示坐标（用于前端显示）
          x: left,
          y: top,
          width,
          height,
          // 原图坐标（用于后端处理）
          originalX,
          originalY,
          originalWidth,
          originalHeight
        });
        common_vendor.index.__f__("log", "at pages/tools/image-watermark-remover.vue:763", "保存水印区域:", {
          显示坐标: { x: left, y: top, width, height },
          原图坐标: { x: originalX, y: originalY, width: originalWidth, height: originalHeight }
        });
        common_vendor.index.showToast({
          title: "水印区域已选择",
          icon: "success"
        });
      }
      this.closeWatermarkSelector();
    },
    clearWatermarkArea() {
      this.selectionBox.visible = false;
      if (this.currentSelectorIndex >= 0) {
        this.$delete(this.watermarkAreas, this.currentSelectorIndex);
      }
    },
    getWatermarkAreaStyle(area, imageIndex) {
      if (!area)
        return {};
      return {
        left: `${area.x}px`,
        top: `${area.y}px`,
        width: `${area.width}px`,
        height: `${area.height}px`
      };
    },
    onTouchStart(e) {
      if (!this.showWatermarkSelector)
        return;
      const touch = e.touches[0];
      this.isDragging = true;
      this.selectionBox.startX = touch.clientX;
      this.selectionBox.startY = touch.clientY;
      this.selectionBox.endX = touch.clientX;
      this.selectionBox.endY = touch.clientY;
      this.selectionBox.visible = true;
    },
    onTouchMove(e) {
      if (!this.isDragging || !this.showWatermarkSelector)
        return;
      const touch = e.touches[0];
      this.selectionBox.endX = touch.clientX;
      this.selectionBox.endY = touch.clientY;
    },
    onTouchEnd(e) {
      if (!this.isDragging || !this.showWatermarkSelector)
        return;
      this.isDragging = false;
    },
    onImageLoad(e) {
      const { width, height } = e.detail;
      this.imageInfo.width = width;
      this.imageInfo.height = height;
      this.imageInfo.displayWidth = width;
      this.imageInfo.displayHeight = height;
    }
  }
};
if (!Array) {
  const _component_Upload = common_vendor.resolveComponent("Upload");
  const _component_ImageIcon = common_vendor.resolveComponent("ImageIcon");
  const _component_RotateCcw = common_vendor.resolveComponent("RotateCcw");
  const _component_Trash2 = common_vendor.resolveComponent("Trash2");
  const _component_Plus = common_vendor.resolveComponent("Plus");
  const _component_Download = common_vendor.resolveComponent("Download");
  (_component_Upload + _component_ImageIcon + _component_RotateCcw + _component_Trash2 + _component_Plus + _component_Download)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      size: 18
    }),
    b: $data.images.length === 0
  }, $data.images.length === 0 ? {
    c: common_vendor.p({
      size: 48
    }),
    d: common_vendor.o((...args) => $options.handleChooseImage && $options.handleChooseImage(...args))
  } : common_vendor.e({
    e: common_vendor.t($data.images.length),
    f: common_vendor.t($data.isWatermarkSelectionMode ? "完成选择" : "选择水印区域"),
    g: common_vendor.o((...args) => $options.toggleWatermarkSelection && $options.toggleWatermarkSelection(...args)),
    h: common_vendor.n($data.isWatermarkSelectionMode ? "bg-blue-500 text-white" : "border border-gray-300 hover-bg-gray-50"),
    i: common_vendor.p({
      size: 16
    }),
    j: common_vendor.o((...args) => $options.handleReset && $options.handleReset(...args)),
    k: $data.isWatermarkSelectionMode
  }, $data.isWatermarkSelectionMode ? {} : {}, {
    l: common_vendor.f($data.imageUrls, (url, index, i0) => {
      return common_vendor.e({
        a: url,
        b: `Preview ${index + 1}`,
        c: $data.watermarkAreas[index]
      }, $data.watermarkAreas[index] ? {
        d: common_vendor.s($options.getWatermarkAreaStyle($data.watermarkAreas[index], index))
      } : {}, {
        e: common_vendor.o(($event) => $data.isWatermarkSelectionMode ? $options.openWatermarkSelector(index) : null, index)
      }, !$data.isWatermarkSelectionMode ? {
        f: "920616e1-3-" + i0,
        g: common_vendor.p({
          size: 12
        }),
        h: common_vendor.o(($event) => $options.removeImage(index), index)
      } : {}, {
        i: common_vendor.t($options.getFileName($data.images[index])),
        j: index
      });
    }),
    m: $data.isWatermarkSelectionMode ? 1 : "",
    n: !$data.isWatermarkSelectionMode,
    o: common_vendor.p({
      size: 24
    }),
    p: common_vendor.o((...args) => $options.handleChooseImage && $options.handleChooseImage(...args))
  }), {
    q: $data.images.length > 0 && $data.processedImages.length === 0
  }, $data.images.length > 0 && $data.processedImages.length === 0 ? {
    r: common_vendor.t($data.isProcessing ? `批量处理中... ${Math.round($data.progress)}%` : "开始批量去水印"),
    s: common_vendor.o((...args) => $options.handleProcess && $options.handleProcess(...args)),
    t: $data.isProcessing
  } : {}, {
    v: $data.isProcessing
  }, $data.isProcessing ? {
    w: common_vendor.t(Math.round($data.progress)),
    x: `${$data.progress}%`
  } : {}, {
    y: $data.processedImages.length > 0
  }, $data.processedImages.length > 0 ? {
    z: common_vendor.f($data.processedImages, (url, index, i0) => {
      return {
        a: url,
        b: `Processed ${index + 1}`,
        c: "920616e1-5-" + i0,
        d: common_vendor.o(($event) => $options.handleDownloadSingle(url, index), index),
        e: index,
        f: common_vendor.o(($event) => $options.handlePreview(url), index)
      };
    }),
    A: common_vendor.p({
      size: 16
    }),
    B: common_vendor.p({
      size: 16
    }),
    C: common_vendor.o((...args) => $options.handleDownloadAll && $options.handleDownloadAll(...args)),
    D: common_vendor.p({
      size: 16
    }),
    E: common_vendor.o((...args) => $options.handleReset && $options.handleReset(...args))
  } : {}, {
    F: $data.showPreview
  }, $data.showPreview ? {
    G: $data.currentPreviewImage,
    H: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args)),
    I: common_vendor.o(($event) => $options.handleDownloadSingle($data.currentPreviewImage, $data.processedImages.indexOf($data.currentPreviewImage))),
    J: common_vendor.o(() => {
    }),
    K: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args))
  } : {}, {
    L: $data.showWatermarkSelector
  }, $data.showWatermarkSelector ? common_vendor.e({
    M: common_vendor.o((...args) => $options.clearWatermarkArea && $options.clearWatermarkArea(...args)),
    N: common_vendor.o((...args) => $options.confirmWatermarkArea && $options.confirmWatermarkArea(...args)),
    O: common_vendor.o((...args) => $options.closeWatermarkSelector && $options.closeWatermarkSelector(...args)),
    P: $data.currentSelectorImage,
    Q: common_vendor.o((...args) => $options.onImageLoad && $options.onImageLoad(...args)),
    R: common_vendor.o((...args) => $options.onTouchStart && $options.onTouchStart(...args)),
    S: common_vendor.o((...args) => $options.onTouchMove && $options.onTouchMove(...args)),
    T: common_vendor.o((...args) => $options.onTouchEnd && $options.onTouchEnd(...args)),
    U: $data.selectionBox.visible
  }, $data.selectionBox.visible ? {
    V: common_vendor.s($options.selectionBoxStyle)
  } : {}, {
    W: common_vendor.o(() => {
    }),
    X: common_vendor.o((...args) => $options.closeWatermarkSelector && $options.closeWatermarkSelector(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-920616e1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/image-watermark-remover.js.map
