"use strict";
const common_vendor = require("../../common/vendor.js");
const config_api = require("../../config/api.js");
const _sfc_main = {
  name: "ImageWatermarkRemover",
  components: {
    Upload: common_vendor.Upload,
    Download: common_vendor.Download,
    ImageIcon: common_vendor.Image,
    RotateCcw: common_vendor.RotateCcw,
    Trash2: common_vendor.Trash2,
    Plus: common_vendor.Plus
  },
  data() {
    return {
      images: [],
      imageUrls: [],
      isProcessing: false,
      processedImages: [],
      progress: 0,
      showPreview: false,
      currentPreviewImage: ""
    };
  },
  methods: {
    handleChooseImage() {
      common_vendor.index.chooseImage({
        count: 9 - this.images.length,
        // 最多可选择的数量为9减去已有图片数
        sizeType: ["original", "compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          const tempFiles = res.tempFiles.map((file) => ({
            ...file,
            path: file.path || file.tempFilePath
          }));
          this.images = [...this.images, ...tempFiles];
          this.imageUrls = [...this.imageUrls, ...tempFilePaths];
          this.processedImages = [];
          this.progress = 0;
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:212", "选择图片失败:", err);
          common_vendor.index.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },
    async handleProcess() {
      if (this.images.length === 0)
        return;
      this.isProcessing = true;
      this.progress = 0;
      try {
        const formData = new FormData();
        this.images.forEach((image, index) => {
          formData.append("files", image);
        });
        formData.append("algorithm", "lama");
        formData.append("outputFormat", "png");
        formData.append("quality", "high");
        formData.append("autoDetect", "true");
        formData.append("preserveDetails", "true");
        const responseData = await config_api.httpRequest.postFormData(
          config_api.API_ENDPOINTS.IMAGE_WATERMARK_REMOVAL.BATCH,
          formData
        );
        if (responseData && responseData.success) {
          const taskId = responseData.data.taskId;
          await this.pollProgress(taskId);
        } else {
          throw new Error((responseData == null ? void 0 : responseData.message) || "处理失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:282", "图片处理失败:", error);
        this.isProcessing = false;
        common_vendor.index.showToast({
          title: "处理失败: " + (error.message || "未知错误"),
          icon: "none"
        });
      }
    },
    async pollProgress(taskId) {
      const pollInterval = setInterval(async () => {
        try {
          const progressResult = await config_api.httpRequest.get(
            `${config_api.API_ENDPOINTS.IMAGE_WATERMARK_REMOVAL.PROGRESS}/${taskId}`
          );
          if (progressResult && progressResult.success) {
            const progressData = progressResult.data;
            this.progress = progressData.progress || 0;
            if (progressData.status === "COMPLETED") {
              clearInterval(pollInterval);
              await this.getProcessResult(taskId);
            } else if (progressData.status === "FAILED") {
              clearInterval(pollInterval);
              throw new Error("处理失败");
            }
          }
        } catch (error) {
          clearInterval(pollInterval);
          common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:312", "获取进度失败:", error);
          this.isProcessing = false;
          common_vendor.index.showToast({
            title: "获取进度失败",
            icon: "none"
          });
        }
      }, 1e3);
    },
    async getProcessResult(taskId) {
      try {
        const resultResult = await config_api.httpRequest.get(
          `${config_api.API_ENDPOINTS.IMAGE_WATERMARK_REMOVAL.RESULT}/${taskId}`
        );
        if (resultResult && resultResult.success) {
          const resultData = resultResult.data;
          if (resultData.results && resultData.results.length > 0) {
            this.processedImages = resultData.results.filter((result) => result.status === "SUCCESS").map((result) => result.processedUrl);
          }
          this.isProcessing = false;
          this.progress = 100;
          common_vendor.index.showToast({
            title: `处理完成！成功: ${resultData.successCount}, 失败: ${resultData.failedCount}`,
            icon: "success"
          });
        } else {
          throw new Error("获取结果失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:348", "获取结果失败:", error);
        this.isProcessing = false;
        common_vendor.index.showToast({
          title: "获取结果失败",
          icon: "none"
        });
      }
    },
    async handleDownloadAll() {
      if (this.processedImages.length === 0) {
        common_vendor.index.showToast({
          title: "没有可下载的图片",
          icon: "none"
        });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "正在保存...",
          mask: true
        });
        const auth = await common_vendor.index.authorize({
          scope: "scope.writePhotosAlbum"
        }).catch(() => null);
        if (!auth) {
          common_vendor.index.hideLoading();
          const res = await common_vendor.index.showModal({
            title: "提示",
            content: "需要保存到相册的权限，是否前往设置？",
            confirmText: "前往设置"
          });
          if (res.confirm) {
            await common_vendor.index.openSetting();
          }
          return;
        }
        let savedCount = 0;
        for (const imageUrl of this.processedImages) {
          try {
            await common_vendor.index.saveImageToPhotosAlbum({
              filePath: imageUrl
            });
            savedCount++;
          } catch (err) {
            common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:402", "保存图片失败:", err);
          }
        }
        common_vendor.index.hideLoading();
        if (savedCount > 0) {
          common_vendor.index.showToast({
            title: `成功保存${savedCount}张图片`,
            icon: "success"
          });
        } else {
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "error"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:445", "下载失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存失败",
          icon: "error"
        });
      }
    },
    handleReset() {
      this.images = [];
      this.imageUrls = [];
      this.processedImages = [];
      this.progress = 0;
      this.isProcessing = false;
    },
    removeImage(index) {
      this.images = this.images.filter((_, i) => i !== index);
      this.imageUrls = this.imageUrls.filter((_, i) => i !== index);
    },
    getFileName(file) {
      if (!file)
        return "未知文件";
      const path = file.path || "";
      const name = path.split("/").pop() || "未知文件";
      return name.length > 15 ? name.substring(0, 15) + "..." : name;
    },
    async handleDownloadSingle(imageUrl, index) {
      try {
        common_vendor.index.showLoading({
          title: "正在保存...",
          mask: true
        });
        const auth = await common_vendor.index.authorize({
          scope: "scope.writePhotosAlbum"
        }).catch(() => null);
        if (!auth) {
          common_vendor.index.hideLoading();
          const res = await common_vendor.index.showModal({
            title: "提示",
            content: "需要保存到相册的权限，是否前往设置？",
            confirmText: "前往设置"
          });
          if (res.confirm) {
            await common_vendor.index.openSetting();
          }
          return;
        }
        await common_vendor.index.saveImageToPhotosAlbum({
          filePath: imageUrl
        });
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "已保存到相册",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/tools/image-watermark-remover.vue:543", "下载失败:", error);
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "保存失败",
          icon: "error"
        });
      }
    },
    // 添加预览方法
    handlePreview(url) {
      common_vendor.index.__f__("log", "at pages/tools/image-watermark-remover.vue:553", "预览图片:", url);
      common_vendor.index.__f__("log", "at pages/tools/image-watermark-remover.vue:554", "当前showPreview状态:", this.showPreview);
      this.showPreview = true;
      this.currentPreviewImage = url;
      common_vendor.index.__f__("log", "at pages/tools/image-watermark-remover.vue:557", "设置后showPreview状态:", this.showPreview);
      common_vendor.index.__f__("log", "at pages/tools/image-watermark-remover.vue:558", "设置后currentPreviewImage:", this.currentPreviewImage);
    },
    closePreview() {
      this.showPreview = false;
      this.currentPreviewImage = "";
    }
  }
};
if (!Array) {
  const _component_Upload = common_vendor.resolveComponent("Upload");
  const _component_ImageIcon = common_vendor.resolveComponent("ImageIcon");
  const _component_RotateCcw = common_vendor.resolveComponent("RotateCcw");
  const _component_Trash2 = common_vendor.resolveComponent("Trash2");
  const _component_Plus = common_vendor.resolveComponent("Plus");
  const _component_Download = common_vendor.resolveComponent("Download");
  (_component_Upload + _component_ImageIcon + _component_RotateCcw + _component_Trash2 + _component_Plus + _component_Download)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.p({
      size: 18
    }),
    b: $data.images.length === 0
  }, $data.images.length === 0 ? {
    c: common_vendor.p({
      size: 48
    }),
    d: common_vendor.o((...args) => $options.handleChooseImage && $options.handleChooseImage(...args))
  } : {
    e: common_vendor.t($data.images.length),
    f: common_vendor.p({
      size: 16
    }),
    g: common_vendor.o((...args) => $options.handleReset && $options.handleReset(...args)),
    h: common_vendor.f($data.imageUrls, (url, index, i0) => {
      return {
        a: url,
        b: `Preview ${index + 1}`,
        c: "920616e1-3-" + i0,
        d: common_vendor.o(($event) => $options.removeImage(index), index),
        e: common_vendor.t($options.getFileName($data.images[index])),
        f: index
      };
    }),
    i: common_vendor.p({
      size: 12
    }),
    j: common_vendor.p({
      size: 24
    }),
    k: common_vendor.o((...args) => $options.handleChooseImage && $options.handleChooseImage(...args))
  }, {
    l: $data.images.length > 0 && $data.processedImages.length === 0
  }, $data.images.length > 0 && $data.processedImages.length === 0 ? {
    m: common_vendor.t($data.isProcessing ? `批量处理中... ${Math.round($data.progress)}%` : "开始批量去水印"),
    n: common_vendor.o((...args) => $options.handleProcess && $options.handleProcess(...args)),
    o: $data.isProcessing
  } : {}, {
    p: $data.isProcessing
  }, $data.isProcessing ? {
    q: common_vendor.t(Math.round($data.progress)),
    r: `${$data.progress}%`
  } : {}, {
    s: $data.processedImages.length > 0
  }, $data.processedImages.length > 0 ? {
    t: common_vendor.f($data.processedImages, (url, index, i0) => {
      return {
        a: url,
        b: `Processed ${index + 1}`,
        c: "920616e1-5-" + i0,
        d: common_vendor.o(($event) => $options.handleDownloadSingle(url, index), index),
        e: index,
        f: common_vendor.o(($event) => $options.handlePreview(url), index)
      };
    }),
    v: common_vendor.p({
      size: 16
    }),
    w: common_vendor.p({
      size: 16
    }),
    x: common_vendor.o((...args) => $options.handleDownloadAll && $options.handleDownloadAll(...args)),
    y: common_vendor.p({
      size: 16
    }),
    z: common_vendor.o((...args) => $options.handleReset && $options.handleReset(...args))
  } : {}, {
    A: $data.showPreview
  }, $data.showPreview ? {
    B: $data.currentPreviewImage,
    C: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args)),
    D: common_vendor.o(($event) => $options.handleDownloadSingle($data.currentPreviewImage, $data.processedImages.indexOf($data.currentPreviewImage))),
    E: common_vendor.o(() => {
    }),
    F: common_vendor.o((...args) => $options.closePreview && $options.closePreview(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-920616e1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/tools/image-watermark-remover.js.map
